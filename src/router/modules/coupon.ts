import type { RouteRecordRaw } from 'vue-router'

const coupons: RouteRecordRaw = {
  path: '/coupon',
  component: () => import('@/layouts/index.vue'),
  // redirect: '/coupons/index',
  name: 'coupons',
  meta: {
    title: '优惠券管理',
    icon: 'i-ep:coupon',
    // auth: '/adm_cinema',
  },
  children: [
    {
      path: '',
      name: 'couponIndex',
      component: () => import('@/views/market/coupon/index.vue'),
      meta: {
        title: '优惠券列表',
        icon: 'ep:coupon',
        // auth: '/coupon/query',
        menu: false,
        breadcrumb: false,
        activeMenu: '/coupon',
      },
    },
  ],
}

export default coupons
