import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/system',
  component: Layout,
  name: 'system',
  meta: {
    title: '系统管理',
    icon: 'i-ri:settings-3-line',
  },
  children: [
    {
      path: 'company',
      name: 'company',
      component: () => import('@/views/user/company.vue'),
      meta: {
        title: '企业管理',
        icon: 'i-ri:building-line',
      },
    },
    {
      path: 'user',
      name: 'user',
      component: () => import('@/views/user/user.vue'),
      meta: {
        title: '用户管理',
        icon: 'i-ri:user-line',
      },
    },
  ],
}

export default routes
