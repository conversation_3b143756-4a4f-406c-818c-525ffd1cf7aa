import type { RouteRecordRaw } from 'vue-router'

const film: RouteRecordRaw = {
  path: '/film',
  component: () => import('@/layouts/index.vue'),
  redirect: '/film/index',
  name: 'Film',
  meta: {
    title: '影片管理',
    icon: 'ep:film',
    // auth: '/adm_cinema',
  },
  children: [
    {
      path: 'index',
      name: 'FilmIndex',
      component: () => import('@/views/cinema/film.vue'),
      meta: {
        title: '影片列表',
        icon: 'ep:film',
        auth: '/data/film',
        menu:false,
        activeMenu: '/film',
      },
    },
  ],
}

export default film
