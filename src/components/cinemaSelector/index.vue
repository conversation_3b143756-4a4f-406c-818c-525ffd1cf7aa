<script setup>
// import Pagination from "@/components/Pagination/index.vue";
import cinemaApi from "@/api/modules/cinema";
// import { listArea } from "@/api/system/Area";

const { proxy } = getCurrentInstance();

const emit = defineEmits();
const props = defineProps({
  // 绑定的影片信息
  modelValue: {
    type: Object,
    default: () => {
      return {};
    },
  },

  customIndex: {
    type: Number,
    default: 0,
  },
  // 是否显示选择数量
  showSelectNum: {
    type: Boolean,
    default: false,
  },
  // 影院名称字段key
  cinemaNameKey: {
    type: String,
    default: "name",
  },
  // 影院别名字段key
  cinemaAliasNameKey: {
    type: String,
    default: "aliasName",
  },
  // 影院编码字段key
  cinemaCodeKey: {
    type: String,
    default: "code",
  },
  // 城市字段key
  cinemaCityKey: {
    type: String,
    default: "cityName",
  },
});
const dialogVisible = ref(false);
const filmDataList = ref([]);
// 选中的影院
const selectedCinemas = ref(JSON.parse(JSON.stringify(props.modelValue)));
//

const total = ref(0);
const searchForm = ref({
  cinemaName: "",
  pageSize: 10,
  pageNum: 1,
  isPartner: "1",
  provinceCode: null,
  cityCode: null,
  cinemaCity: [],
});
const multipleTableRef = ref(null);

const loading = ref(false);
const query = () => {
  loading.value = true;
  cinemaApi.getCinemaList(searchForm.value).then((res) => {
    loading.value = false;
    const { total: newTotal, rows, code } = res;
    if (code === 200) {
      filmDataList.value = rows.map((item) => {
        return {
          id: item.id,
          name: item.cinemaName,
          aliasName: item.aliasName,
          tsCode: item.tsCode,
          code: item.cinemaCode,
          cityName: item.cityName,
          isPartner: item.isPartner,
        };
      });
      total.value = newTotal;

      // updateSelectStatus()
    }
  });
};
/**
 * @description 加载影院列表
 * */
onMounted(() => {
  query();
  // getProvinceList(1).then(() => {
  //   getProvinceList(2);
  // });
});

const cityList = ref([]);
// / 加载省市列表 level 1省 2市
const getProvinceList = (level) => {
  return new Promise((resolve, reject) => {
    listArea({ level: level }).then((response) => {
      if (level === 1) {
        cityList.value = response.data.map((item) => {
          return {
            value: item.code || "",
            label: item.name,
            id: item.id,
            children: [{ value: "", label: "全部", id: "" }],
          };
        });
      } else {
        const { data } = response;
        data.map((item, index) => {
          cityList.value.map((city, index) => {
            if (item.parentId === city.id) {
              cityList.value[index].children.push({
                value: item.code || "",
                label: item.name,
                id: item.id,
              });
            }
          });
        });
      }
      resolve();
    });
  });
};
// 选中城市
const selectCity = (value) => {
  if (value === null) {
    searchForm.value.provinceCode = null;
    searchForm.value.cityCode = null;
  } else {
    searchForm.value.provinceCode = value[0];
    searchForm.value.cityCode = value[1];
  }
};

// 更新选中状态
const updateSelectStatus = () => {
  if (!dialogVisible.value) return;

  const selectedIds = selectedCinemas.value.map((item) => item.id);
  nextTick(() => {
    filmDataList.value.forEach((row) => {
      if (selectedIds.includes(row.id)) {
        multipleTableRef.value.toggleRowSelection(row, true);
      }
    });
  });
};

const handleShowSelectPopop = () => {
  dialogVisible.value = true;
  updateSelectStatus();
};

const handleSelectionChange = (cinemas) => {
  // selectedCinemas.value = cinemas
  // 1.如果cinemas的长度大于0，说明有选中的影院
  // 2.如果cinemas的长度等于0，说明没有选中的影院，需要拿filmDataList.value去匹配modelValue，
  // 如果匹配到了，就删除
  // console.log("selectedCinemas", selectedCinemas.value);
  // proxy.emit("update:modelValue", selectedCinemas.value);
};
const handleAdd = (cinema) => {
  console.log("add", cinema);
  if (selectedCinemas.value.find((item) => item.id === cinema.id)) {
    proxy?.$modal.msgError("已选择该影院");
    return;
  }
  selectedCinemas.value.push({
    id: cinema.id,
    [props.cinemaNameKey]: cinema.name,
    [props.cinemaAliasNameKey]: cinema.aliasName,
    [props.cinemaCodeKey]: cinema.code,
    [props.cinemaCityKey]: cinema.cityName,
  });

  // proxy.emit("update:modelValue", selectedCinemas.value);
};
// 全选
const addAllToSelected = () => {
  filmDataList.value.forEach((item) => {
    if (!selectedCinemas.value.find((cinema) => cinema.id === item.id)) {
      // selectedCinemas.value.push(item)
      handleAdd(item);
    }
  });
};

// 清空
const clearSelected = () => {
  proxy.$modal
    .confirm("确认清空吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      selectedCinemas.value = [];
    });
};
/**
 * @description 确认
 * @param cinemas 选中的影院
 */
const confirm = () => {
  dialogVisible.value = false;
  // proxy.emit("update:modelValue", selectedCinemas.value);
  // proxy.emit("onComplete", selectedCinemas.value);
  emit("onComplete", selectedCinemas.value, props.customIndex);
};
// 取消
const cancel = (row) => {
  const index = selectedCinemas.value.indexOf(row);
  const deleteData = filmDataList.value.find((item) => item.id === row.id);
  if (index !== -1) {
    selectedCinemas.value.splice(index, 1);
  }
  if (deleteData) {
    multipleTableRef.value.toggleRowSelection(deleteData, false);
  }
};
</script>

<template>
  <div>
    <slot>
      <div class="cinema-selector">
        <el-button type="primary" @click="handleShowSelectPopop"
          >选择影院</el-button
        >
        <span
          v-if="showSelectNum && selectedCinemas.length"
          style="margin-left: 8px;"
          >已选择{{ selectedCinemas.length }}个影院</span
        >
      </div>
    </slot>
    <el-dialog v-model="dialogVisible" width="90%" draggable title="请选择影院">
      <div>
        <el-row>
          <el-col :span="24">
            <el-form inline v-model="searchForm" style="margin-bottom: 24px;">
              <el-form-item label="影院名称:">
                <el-input
                  placeholder="请输入"
                  v-model="searchForm.cinemaName"
                  clearable
                ></el-input>
              </el-form-item>
              <!--      影院城市-->
              <el-form-item label="影院城市">
                <el-cascader
                  v-model="searchForm.cinemaCity"
                  :options="cityList"
                  placeholder="按选择"
                  @change="selectCity"
                  clearable
                />
              </el-form-item>
              <!--            合作状态-->
              <el-form-item label="合作状态:">
                <el-select v-model="searchForm.isPartner" placeholder="请选择">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="合作" value="1"></el-option>
                  <el-option label="非合作" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="search" plain @click="query"
                  >查询</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-table
              :data="filmDataList"
              size="small"
              v-loading="loading"
              ref="multipleTableRef"
              border
              stripe
              @selection-change="handleSelectionChange"
              :row-key="(row) => row.code"
            >
              <!-- 影院id -->
              <el-table-column
                prop="id"
                label="影院id"
                align="center"
                width="60"
              ></el-table-column>
              <el-table-column label="影院名称">
                <template #default="{ row }">
                  <el-text>{{ row.name }}</el-text>
                </template>
              </el-table-column>
              <el-table-column label="影院别名">
                <template #default="{ row }">
                  <el-text type="info">{{ row.aliasName }}</el-text>
                </template>
              </el-table-column>
              <!-- 影院编码 -->
              <el-table-column
                prop="code"
                label="影院编码"
                align="center"
                width="80"
              ></el-table-column>
              <!--            cityName-->
              <el-table-column
                prop="cityName"
                label="城市"
                align="center"
                width="80"
              ></el-table-column>
              <!-- 影院状态-->
              <!-- 合作状态-->
              <el-table-column label="合作状态" align="center" width="90">
                <template #default="{ row }">
                  <el-tag
                    :type="row.isPartner === 1 ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ row.isPartner === 1 ? "合作" : "非合作" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" width="55" label="加入">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    icon="Plus"
                    size="small"
                    plain
                    circle
                    @click="handleAdd(row)"
                  />
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px; text-align: right;">
              <el-button
                type="primary"
                plain
                size="small"
                @click="addAllToSelected"
                >批量添加本页</el-button
              >
            </div>
            <!-- 分页 -->
            <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              v-model:page="searchForm.pageNum"
              v-model:limit="searchForm.pageSize"
              @pagination="query"
            />
          </el-col>
          <el-col :span="8">
            <el-scrollbar>
              <div>
                <el-table :data="selectedCinemas" size="small">
                  <el-table-column
                    :prop="cinemaNameKey"
                    label="影院名称"
                    width="150"
                  >
                  </el-table-column>
                  <el-table-column
                    :prop="cinemaCityKey"
                    label="城市"
                    align="center"
                  ></el-table-column>
                  <!--              删除-->
                  <el-table-column label="操作" align="center" width="100">
                    <template #default="{ row }">
                      <el-button
                        type="danger"
                        circle
                        icon="Delete"
                        size="small"
                        @click="cancel(row)"
                      ></el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div
                style="margin-top: 10px; text-align: right;"
                v-if="selectedCinemas.length > 0"
              >
                <el-button
                  type="primary"
                  plain
                  size="small"
                  @click="clearSelected"
                  >清空选择</el-button
                >
              </div>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
