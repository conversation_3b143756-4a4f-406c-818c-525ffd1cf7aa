
<script setup>
// import { getCurrentInstance, ref, watch } from "vue";
const { proxy } = getCurrentInstance();
const { bus_film_type } = proxy.useDict("bus_film_type");

const props = defineProps({
  // 默认已选中的电影版本
  defaultChecked: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
const checkList = ref(props.defaultChecked);
const checkAll = ref(false); // 全选
const isIndeterminate = ref(false); // 不确定
const handleCheckAllChange = (val) => {
  checkList.value = val ? bus_film_type.value.map(item=>{
    return item.value
  }) : [];
  isIndeterminate.value = false;

};

const handleCheckedCitiesChange = (value) => {
  // console.log(bus_film_type.value.length,value.length);
  if (value.length === bus_film_type.value.length) {
    checkAll.value = true;
    isIndeterminate.value = false;
  } else if (value.length > 0) {
    checkAll.value = false;
    isIndeterminate.value = true;
  } else {
    checkAll.value = false;
    isIndeterminate.value = false;
  }
  // console.log(value, checkAll.value, isIndeterminate.value);
};

// 监听checkList
watch(checkList, (newVal, oldVal) => {
  // console.log("checkList", newVal, oldVal);
  // console.log("checkList", checkList.value);
  // 添加了那些
  const addList = newVal.filter((item) => {
    return !oldVal.includes(item);
  });
  // 删除了那些
  const delList = oldVal.filter((item) => {
    return !newVal.includes(item);
  });
  if (addList.length > 0) {
    proxy.$emit("add", addList);
  }
  if (delList.length > 0) {
    proxy.$emit("del", delList);
  }
  // TODO: 1.添加了那些 2.删除了那些

});

</script>
<template>
  <div>
<!--     已选{{ props.defaultChecked }}-->
    <el-checkbox
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >
      全选
    </el-checkbox>
    <el-checkbox-group v-model="checkList" @change="handleCheckedCitiesChange">
      <el-checkbox
        v-for="item in bus_film_type"
        :key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>


<style scoped>
/* Add your component styles here */
</style>
