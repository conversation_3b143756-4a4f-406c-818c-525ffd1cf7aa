<!--
 * 作者：zhang
 * 时间：2023/10/26 13:33
 * 功能：周期事件选择器
 *
-->
<script setup>
import {getCurrentInstance, onBeforeUpdate} from "vue";

const {proxy} = getCurrentInstance();
const props = defineProps({
  //  传入的值 例如：[{week: ["1", "2", "3", "4", "7"], time: ["00:00:00", "13:59:59"]}]
  modelValue: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

const emit = defineEmits(["update:modelValue"]);

// const dataList = ref([]);
onBeforeUpdate(() => {
  console.log('加载周期事件选择器', props.modelValue)
  // props.modelValue = props.modelValue.map((item) => {
  //   // console.log(item.time);
  //   return {
  //     week: item.week,
  //     time: [
  //       new Date("1999-01-01 " + item.time[0]),
  //       new Date("1999-01-01 " + item.time[1]),
  //     ],
  //   };
  // });
  // console.log("AA",props.modelValue );
});

const weekList = [
  {label: "星期一", value: 1},
  {label: "星期二", value: 2},
  {label: "星期三", value: 3},
  {label: "星期四", value: 4},
  {label: "星期五", value: 5},
  {label: "星期六", value: 6},
  {label: "星期日", value: 7}
];
/**
 * @description 添加一组
 * @returns {Promise<void>} 无返回值
 */
const add = () => {
  console.log(props.modelValue);
  
  props.modelValue.push({
    week: [1, 2, 3, 4, 5, 6, 7],// 星期一到星期日
    time: [new Date("1999-01-01 00:00:00"), new Date("2000-01-01 23:59:59")], // 时分秒 00:00:00 - 23:59:59
  });
  emit("update:modelValue", props.modelValue);
};

/**
 * @description 更新数据
 * @returns {Promise<void>} 无返回值
 */
const update = () => {
  const newData = props.modelValue.map((item) => {
    return {
      week: item.week,
      time: [
        item.time[0].toLocaleTimeString() || "00:00:00",
        item.time[1].toLocaleTimeString() || "00:00:00",
      ],
    };
  });
  // console.log(newData)
  emit("update:modelValue", newData);
};
</script>

<template>
  <!--  {{ props.modelValue }}-->
  <div v-if="props.modelValue.length === 0" style="width: 100%">不限</div>
  <el-card
      v-for="(week, index) in props.modelValue"
      shadow="hover"
      style="margin-bottom: 0.5rem;width: 100%"
  >
    <template #header>
      <el-row  class="header-row">
        <el-col :span="18"> 第{{ index + 1 }}组</el-col>
        <el-col :span="6" style="text-align: right">
          <el-button
              type="danger"
              icon="Delete"
              size="small"
              plain
              @click="() => props.modelValue.splice(index, 1)"
          >删除
          </el-button>
        </el-col>
      </el-row>
    </template>
    <el-row>
      <el-col :span="25">
        <el-checkbox-group v-model="week.week">
          <el-checkbox
              v-for="item in weekList"
              :value="item.value"
              :key="item.value"
          >{{ item.label }}
          </el-checkbox
          >
        </el-checkbox-group>
      </el-col>
      <el-col :span="15">
<!--        {{week.time}}-->
<!--&lt;!&ndash;        <el-time-picker&ndash;&gt;-->
<!--&lt;!&ndash;            is-range&ndash;&gt;-->
<!--&lt;!&ndash;            format="HH:mm"&ndash;&gt;-->
<!--&lt;!&ndash;            value-format="HH:mm"&ndash;&gt;-->
<!--&lt;!&ndash;            v-model="week.time"&ndash;&gt;-->
<!--&lt;!&ndash;            range-separator="至"&ndash;&gt;-->
<!--&lt;!&ndash;            start-placeholder="开始时间"&ndash;&gt;-->
<!--&lt;!&ndash;            end-placeholder="结束时间"&ndash;&gt;-->
<!--&lt;!&ndash;        >&ndash;&gt;-->
<!--&lt;!&ndash;        </el-time-picker>&ndash;&gt;-->
        <el-time-picker
            is-range
            v-model="week.time"
            size="small"
            format="HH:mm"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="week.time"
        >
        </el-time-picker>
      </el-col>
    </el-row>
  </el-card>
  <el-row>
    <el-col>
      <!-- 添加一组 -->
      <el-button type="primary" icon="Plus" plain @click="add">
        添加一组
      </el-button>
    </el-col>
  </el-row>
</template>

<style scoped="scoped" lang="scss">
.week-card .header-row {
  padding: 0px 8px; /* 调整内间距 */
}
</style>
