# 文档

## 【后台】商品管理


### 商品列表
接口权限：/store/product/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/list


描述：商品列表
接口权限：/store/product/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| name | string | 否 | - | 商品名称(搜索项) |  |
| productType | int32 | 否 | - | 商品类型 | 0 |
| categoryId | string | 否 | - | 商品分类ID |  |
| status | int32 | 否 | - | 商品状态 | 0 |
| source | int32 | 否 | - | 商品来源 | 0 |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "name": "",
    "productType": 0,
    "categoryId": "",
    "status": 0,
    "source": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | 商品名称 |  |
|   └ productType | enum | 否 | - | 商品类型 ProductType code<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ productTypeDesc | enum | 否 | - | 商品类型 ProductType desc<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ categoryId | string | 否 | - | 商品分类ID |  |
|   └ skuList | array | 否 |  | 商品SKU列表 |  |
|     └ id | string | 否 | - | SKU唯一标识 |  |
|     └ productId | string | 否 | - | 商品ID |  |
|     └ name | string | 否 | - | SKU 名称 |  |
|     └ paramList | array | 否 |  | SKU参数列表 |  |
|       └ specName | string | 否 | - | 商品规格名称 |  |
|       └ attrName | string | 否 | - | 商品规格属性名称 |  |
|       └ attrValue | string | 否 | - | 商品规格属性值 |  |
|     └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|     └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|     └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|     └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|     └ image | string | 否 | - | 图片 |  |
|     └ inventory | object | 否 |  | 库存信息 |  |
|       └ id | string | 否 | - | 库存唯一标识 |  |
|       └ stock | int32 | 否 | - | 库存 | 0 |
|       └ salesCount | int64 | 否 | - | 销量 | 0 |
|       └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|       └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|       └ createAt | int64 | 否 | - | 创建时间 | 0 |
|       └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|     └ weight | int64 | 否 | - | 重量（kg） | 0 |
|     └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|     └ default | boolean | 否 | - | 是否默认 | true |
|     └ visible | boolean | 否 | - | 是否展示 | true |
|     └ barcode | string | 否 | - | 商品条码 |  |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ status | int32 | 否 | - | 商品状态 ProductStatus code | 0 |
|   └ statusDesc | string | 否 | - | 商品状态 ProductStatus desc |  |
|   └ source | int32 | 否 | - | 商品来源 ProductSource code | 0 |
|   └ sourceDesc | string | 否 | - | 商品来源 ProductSource desc |  |
|   └ mainImage | string | 否 | - | 主图片 |  |
|   └ images | array | 否 | - | 商品图片列表 | , |
|   └ detail | string | 否 | - | 商品详情 |  |
|   └ salesCount | int64 | 否 | - | 销量 | 0 |
|   └ initialSalesCount | int64 | 否 | - | 初始销量 | 0 |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ externalId | string | 否 | - | 外部商品ID（天猫超市/第三方商品） |  |
|   └ delFlag | int64 | 否 | - | 是否删除 | 0 |
|   └ description | string | 否 | - | 商品简介 |  |
|   └ specType | enum | 否 | - | 规格类型 SpecType code<br/>(See: 规格类型枚举) | SINGLE |
|   └ specTypeDesc | enum | 否 | - | 规格类型 SpecType desc<br/>(See: 规格类型枚举) | SINGLE |
|   └ deliveryMethod | enum | 否 | - | 配送方式 DeliveryMethod code<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ deliveryMethodDesc | enum | 否 | - | 配送方式 DeliveryMethod desc<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ checkOrderFormIdList | array | 否 | - | 下单信息表单项ID列表 | , |
|   └ formListEmpty | boolean | 否 | - | 下单信息表单项列表是否为空的标记 | true |
|   └ sortOrder | int32 | 否 | - | 排序 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | 商品名称 |  |
|   └ productType | enum | 否 | - | 商品类型 ProductType code<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ productTypeDesc | enum | 否 | - | 商品类型 ProductType desc<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ categoryId | string | 否 | - | 商品分类ID |  |
|   └ skuList | array | 否 |  | 商品SKU列表 |  |
|     └ id | string | 否 | - | SKU唯一标识 |  |
|     └ productId | string | 否 | - | 商品ID |  |
|     └ name | string | 否 | - | SKU 名称 |  |
|     └ paramList | array | 否 |  | SKU参数列表 |  |
|       └ specName | string | 否 | - | 商品规格名称 |  |
|       └ attrName | string | 否 | - | 商品规格属性名称 |  |
|       └ attrValue | string | 否 | - | 商品规格属性值 |  |
|     └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|     └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|     └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|     └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|     └ image | string | 否 | - | 图片 |  |
|     └ inventory | object | 否 |  | 库存信息 |  |
|       └ id | string | 否 | - | 库存唯一标识 |  |
|       └ stock | int32 | 否 | - | 库存 | 0 |
|       └ salesCount | int64 | 否 | - | 销量 | 0 |
|       └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|       └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|       └ createAt | int64 | 否 | - | 创建时间 | 0 |
|       └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|     └ weight | int64 | 否 | - | 重量（kg） | 0 |
|     └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|     └ default | boolean | 否 | - | 是否默认 | true |
|     └ visible | boolean | 否 | - | 是否展示 | true |
|     └ barcode | string | 否 | - | 商品条码 |  |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ status | int32 | 否 | - | 商品状态 ProductStatus code | 0 |
|   └ statusDesc | string | 否 | - | 商品状态 ProductStatus desc |  |
|   └ source | int32 | 否 | - | 商品来源 ProductSource code | 0 |
|   └ sourceDesc | string | 否 | - | 商品来源 ProductSource desc |  |
|   └ mainImage | string | 否 | - | 主图片 |  |
|   └ images | array | 否 | - | 商品图片列表 | , |
|   └ detail | string | 否 | - | 商品详情 |  |
|   └ salesCount | int64 | 否 | - | 销量 | 0 |
|   └ initialSalesCount | int64 | 否 | - | 初始销量 | 0 |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ externalId | string | 否 | - | 外部商品ID（天猫超市/第三方商品） |  |
|   └ delFlag | int64 | 否 | - | 是否删除 | 0 |
|   └ description | string | 否 | - | 商品简介 |  |
|   └ specType | enum | 否 | - | 规格类型 SpecType code<br/>(See: 规格类型枚举) | SINGLE |
|   └ specTypeDesc | enum | 否 | - | 规格类型 SpecType desc<br/>(See: 规格类型枚举) | SINGLE |
|   └ deliveryMethod | enum | 否 | - | 配送方式 DeliveryMethod code<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ deliveryMethodDesc | enum | 否 | - | 配送方式 DeliveryMethod desc<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ checkOrderFormIdList | array | 否 | - | 下单信息表单项ID列表 | , |
|   └ formListEmpty | boolean | 否 | - | 下单信息表单项列表是否为空的标记 | true |
|   └ sortOrder | int32 | 否 | - | 排序 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "productType": "PHYSICAL",
            "productTypeDesc": "PHYSICAL",
            "categoryId": "",
            "skuList": [
                {
                    "id": "",
                    "productId": "",
                    "name": "",
                    "paramList": [
                        {
                            "specName": "",
                            "attrName": "",
                            "attrValue": ""
                        }
                    ],
                    "price": 0,
                    "memberPrice": 0,
                    "originalPrice": 0,
                    "costPrice": 0,
                    "image": "",
                    "inventory": {
                        "id": "",
                        "stock": 0,
                        "salesCount": 0,
                        "alertStock": 0,
                        "lockedStock": 0,
                        "createAt": 0,
                        "updateAt": 0
                    },
                    "weight": 0,
                    "volume": 0,
                    "default": true,
                    "visible": true,
                    "barcode": "",
                    "createAt": 0,
                    "updateAt": 0
                }
            ],
            "status": 0,
            "statusDesc": "",
            "source": 0,
            "sourceDesc": "",
            "mainImage": "",
            "images": [
                0,
                0
            ],
            "detail": "",
            "salesCount": 0,
            "initialSalesCount": 0,
            "createAt": 0,
            "updateAt": 0,
            "externalId": "",
            "delFlag": 0,
            "description": "",
            "specType": "SINGLE",
            "specTypeDesc": "SINGLE",
            "deliveryMethod": "MERCHANT_DELIVERY",
            "deliveryMethodDesc": "MERCHANT_DELIVERY",
            "checkOrderFormIdList": [
                0,
                0
            ],
            "formListEmpty": true,
            "sortOrder": 0
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "productType": "PHYSICAL",
            "productTypeDesc": "PHYSICAL",
            "categoryId": "",
            "skuList": [
                {
                    "id": "",
                    "productId": "",
                    "name": "",
                    "paramList": [
                        {
                            "specName": "",
                            "attrName": "",
                            "attrValue": ""
                        }
                    ],
                    "price": 0,
                    "memberPrice": 0,
                    "originalPrice": 0,
                    "costPrice": 0,
                    "image": "",
                    "inventory": {
                        "id": "",
                        "stock": 0,
                        "salesCount": 0,
                        "alertStock": 0,
                        "lockedStock": 0,
                        "createAt": 0,
                        "updateAt": 0
                    },
                    "weight": 0,
                    "volume": 0,
                    "default": true,
                    "visible": true,
                    "barcode": "",
                    "createAt": 0,
                    "updateAt": 0
                }
            ],
            "status": 0,
            "statusDesc": "",
            "source": 0,
            "sourceDesc": "",
            "mainImage": "",
            "images": [
                0,
                0
            ],
            "detail": "",
            "salesCount": 0,
            "initialSalesCount": 0,
            "createAt": 0,
            "updateAt": 0,
            "externalId": "",
            "delFlag": 0,
            "description": "",
            "specType": "SINGLE",
            "specTypeDesc": "SINGLE",
            "deliveryMethod": "MERCHANT_DELIVERY",
            "deliveryMethodDesc": "MERCHANT_DELIVERY",
            "checkOrderFormIdList": [
                0,
                0
            ],
            "formListEmpty": true,
            "sortOrder": 0
        }
    ]
}
```

#### 错误码

无

### 搜索商品
接口权限：/store/product/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/search


描述：搜索商品
接口权限：/store/product/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| name | string | 否 | - | 商品名称（模糊搜索） |  |
| productType | int32 | 否 | - | 商品类型 | 0 |
| categoryId | string | 否 | - | 商品分类ID |  |
| status | int32 | 否 | - | 商品状态 | 0 |
| source | int32 | 否 | - | 商品来源 | 0 |
| page | int32 | 是 | - | 页码 | 0 |
| size | int32 | 是 | - | 每页数量 | 0 |
| sortField | string | 否 | - | 排序字段<br>可选值：createAt（创建时间）、salesCount（销量） |  |
| sortDirection | string | 否 | - | 排序方向<br>可选值：asc（升序）、desc（降序） |  |

#### 请求示例

```
{
    "name": "",
    "productType": 0,
    "categoryId": "",
    "status": 0,
    "source": 0,
    "page": 0,
    "size": 0,
    "sortField": "",
    "sortDirection": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | 商品名称 |  |
|   └ productType | enum | 否 | - | 商品类型 ProductType code<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ productTypeDesc | enum | 否 | - | 商品类型 ProductType desc<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ categoryId | string | 否 | - | 商品分类ID |  |
|   └ skuList | array | 否 |  | 商品SKU列表 |  |
|     └ id | string | 否 | - | SKU唯一标识 |  |
|     └ productId | string | 否 | - | 商品ID |  |
|     └ name | string | 否 | - | SKU 名称 |  |
|     └ paramList | array | 否 |  | SKU参数列表 |  |
|       └ specName | string | 否 | - | 商品规格名称 |  |
|       └ attrName | string | 否 | - | 商品规格属性名称 |  |
|       └ attrValue | string | 否 | - | 商品规格属性值 |  |
|     └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|     └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|     └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|     └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|     └ image | string | 否 | - | 图片 |  |
|     └ inventory | object | 否 |  | 库存信息 |  |
|       └ id | string | 否 | - | 库存唯一标识 |  |
|       └ stock | int32 | 否 | - | 库存 | 0 |
|       └ salesCount | int64 | 否 | - | 销量 | 0 |
|       └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|       └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|       └ createAt | int64 | 否 | - | 创建时间 | 0 |
|       └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|     └ weight | int64 | 否 | - | 重量（kg） | 0 |
|     └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|     └ default | boolean | 否 | - | 是否默认 | true |
|     └ visible | boolean | 否 | - | 是否展示 | true |
|     └ barcode | string | 否 | - | 商品条码 |  |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ status | int32 | 否 | - | 商品状态 ProductStatus code | 0 |
|   └ statusDesc | string | 否 | - | 商品状态 ProductStatus desc |  |
|   └ source | int32 | 否 | - | 商品来源 ProductSource code | 0 |
|   └ sourceDesc | string | 否 | - | 商品来源 ProductSource desc |  |
|   └ mainImage | string | 否 | - | 主图片 |  |
|   └ images | array | 否 | - | 商品图片列表 | , |
|   └ detail | string | 否 | - | 商品详情 |  |
|   └ salesCount | int64 | 否 | - | 销量 | 0 |
|   └ initialSalesCount | int64 | 否 | - | 初始销量 | 0 |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ externalId | string | 否 | - | 外部商品ID（天猫超市/第三方商品） |  |
|   └ delFlag | int64 | 否 | - | 是否删除 | 0 |
|   └ description | string | 否 | - | 商品简介 |  |
|   └ specType | enum | 否 | - | 规格类型 SpecType code<br/>(See: 规格类型枚举) | SINGLE |
|   └ specTypeDesc | enum | 否 | - | 规格类型 SpecType desc<br/>(See: 规格类型枚举) | SINGLE |
|   └ deliveryMethod | enum | 否 | - | 配送方式 DeliveryMethod code<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ deliveryMethodDesc | enum | 否 | - | 配送方式 DeliveryMethod desc<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ checkOrderFormIdList | array | 否 | - | 下单信息表单项ID列表 | , |
|   └ formListEmpty | boolean | 否 | - | 下单信息表单项列表是否为空的标记 | true |
|   └ sortOrder | int32 | 否 | - | 排序 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | 商品名称 |  |
|   └ productType | enum | 否 | - | 商品类型 ProductType code<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ productTypeDesc | enum | 否 | - | 商品类型 ProductType desc<br/>(See: 商品类型枚举) | PHYSICAL |
|   └ categoryId | string | 否 | - | 商品分类ID |  |
|   └ skuList | array | 否 |  | 商品SKU列表 |  |
|     └ id | string | 否 | - | SKU唯一标识 |  |
|     └ productId | string | 否 | - | 商品ID |  |
|     └ name | string | 否 | - | SKU 名称 |  |
|     └ paramList | array | 否 |  | SKU参数列表 |  |
|       └ specName | string | 否 | - | 商品规格名称 |  |
|       └ attrName | string | 否 | - | 商品规格属性名称 |  |
|       └ attrValue | string | 否 | - | 商品规格属性值 |  |
|     └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|     └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|     └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|     └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|     └ image | string | 否 | - | 图片 |  |
|     └ inventory | object | 否 |  | 库存信息 |  |
|       └ id | string | 否 | - | 库存唯一标识 |  |
|       └ stock | int32 | 否 | - | 库存 | 0 |
|       └ salesCount | int64 | 否 | - | 销量 | 0 |
|       └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|       └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|       └ createAt | int64 | 否 | - | 创建时间 | 0 |
|       └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|     └ weight | int64 | 否 | - | 重量（kg） | 0 |
|     └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|     └ default | boolean | 否 | - | 是否默认 | true |
|     └ visible | boolean | 否 | - | 是否展示 | true |
|     └ barcode | string | 否 | - | 商品条码 |  |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ status | int32 | 否 | - | 商品状态 ProductStatus code | 0 |
|   └ statusDesc | string | 否 | - | 商品状态 ProductStatus desc |  |
|   └ source | int32 | 否 | - | 商品来源 ProductSource code | 0 |
|   └ sourceDesc | string | 否 | - | 商品来源 ProductSource desc |  |
|   └ mainImage | string | 否 | - | 主图片 |  |
|   └ images | array | 否 | - | 商品图片列表 | , |
|   └ detail | string | 否 | - | 商品详情 |  |
|   └ salesCount | int64 | 否 | - | 销量 | 0 |
|   └ initialSalesCount | int64 | 否 | - | 初始销量 | 0 |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ externalId | string | 否 | - | 外部商品ID（天猫超市/第三方商品） |  |
|   └ delFlag | int64 | 否 | - | 是否删除 | 0 |
|   └ description | string | 否 | - | 商品简介 |  |
|   └ specType | enum | 否 | - | 规格类型 SpecType code<br/>(See: 规格类型枚举) | SINGLE |
|   └ specTypeDesc | enum | 否 | - | 规格类型 SpecType desc<br/>(See: 规格类型枚举) | SINGLE |
|   └ deliveryMethod | enum | 否 | - | 配送方式 DeliveryMethod code<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ deliveryMethodDesc | enum | 否 | - | 配送方式 DeliveryMethod desc<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
|   └ checkOrderFormIdList | array | 否 | - | 下单信息表单项ID列表 | , |
|   └ formListEmpty | boolean | 否 | - | 下单信息表单项列表是否为空的标记 | true |
|   └ sortOrder | int32 | 否 | - | 排序 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "productType": "PHYSICAL",
            "productTypeDesc": "PHYSICAL",
            "categoryId": "",
            "skuList": [
                {
                    "id": "",
                    "productId": "",
                    "name": "",
                    "paramList": [
                        {
                            "specName": "",
                            "attrName": "",
                            "attrValue": ""
                        }
                    ],
                    "price": 0,
                    "memberPrice": 0,
                    "originalPrice": 0,
                    "costPrice": 0,
                    "image": "",
                    "inventory": {
                        "id": "",
                        "stock": 0,
                        "salesCount": 0,
                        "alertStock": 0,
                        "lockedStock": 0,
                        "createAt": 0,
                        "updateAt": 0
                    },
                    "weight": 0,
                    "volume": 0,
                    "default": true,
                    "visible": true,
                    "barcode": "",
                    "createAt": 0,
                    "updateAt": 0
                }
            ],
            "status": 0,
            "statusDesc": "",
            "source": 0,
            "sourceDesc": "",
            "mainImage": "",
            "images": [
                0,
                0
            ],
            "detail": "",
            "salesCount": 0,
            "initialSalesCount": 0,
            "createAt": 0,
            "updateAt": 0,
            "externalId": "",
            "delFlag": 0,
            "description": "",
            "specType": "SINGLE",
            "specTypeDesc": "SINGLE",
            "deliveryMethod": "MERCHANT_DELIVERY",
            "deliveryMethodDesc": "MERCHANT_DELIVERY",
            "checkOrderFormIdList": [
                0,
                0
            ],
            "formListEmpty": true,
            "sortOrder": 0
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "productType": "PHYSICAL",
            "productTypeDesc": "PHYSICAL",
            "categoryId": "",
            "skuList": [
                {
                    "id": "",
                    "productId": "",
                    "name": "",
                    "paramList": [
                        {
                            "specName": "",
                            "attrName": "",
                            "attrValue": ""
                        }
                    ],
                    "price": 0,
                    "memberPrice": 0,
                    "originalPrice": 0,
                    "costPrice": 0,
                    "image": "",
                    "inventory": {
                        "id": "",
                        "stock": 0,
                        "salesCount": 0,
                        "alertStock": 0,
                        "lockedStock": 0,
                        "createAt": 0,
                        "updateAt": 0
                    },
                    "weight": 0,
                    "volume": 0,
                    "default": true,
                    "visible": true,
                    "barcode": "",
                    "createAt": 0,
                    "updateAt": 0
                }
            ],
            "status": 0,
            "statusDesc": "",
            "source": 0,
            "sourceDesc": "",
            "mainImage": "",
            "images": [
                0,
                0
            ],
            "detail": "",
            "salesCount": 0,
            "initialSalesCount": 0,
            "createAt": 0,
            "updateAt": 0,
            "externalId": "",
            "delFlag": 0,
            "description": "",
            "specType": "SINGLE",
            "specTypeDesc": "SINGLE",
            "deliveryMethod": "MERCHANT_DELIVERY",
            "deliveryMethodDesc": "MERCHANT_DELIVERY",
            "checkOrderFormIdList": [
                0,
                0
            ],
            "formListEmpty": true,
            "sortOrder": 0
        }
    ]
}
```

#### 错误码

无

### 根据ID获取商品详情
接口权限：/store/product/detail

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/detail/{id}


描述：根据ID获取商品详情
接口权限：/store/product/detail

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### Path参数

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| id | 是 | No comments found. |  |

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 商品ID |  |
| name | string | 否 | - | 商品名称 |  |
| productType | enum | 否 | - | 商品类型 ProductType code<br/>(See: 商品类型枚举) | PHYSICAL |
| productTypeDesc | enum | 否 | - | 商品类型 ProductType desc<br/>(See: 商品类型枚举) | PHYSICAL |
| categoryId | string | 否 | - | 商品分类ID |  |
| skuList | array | 否 |  | 商品SKU列表 |  |
|   └ id | string | 否 | - | SKU唯一标识 |  |
|   └ productId | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | SKU 名称 |  |
|   └ paramList | array | 否 |  | SKU参数列表 |  |
|     └ specName | string | 否 | - | 商品规格名称 |  |
|     └ attrName | string | 否 | - | 商品规格属性名称 |  |
|     └ attrValue | string | 否 | - | 商品规格属性值 |  |
|   └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|   └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|   └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|   └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|   └ image | string | 否 | - | 图片 |  |
|   └ inventory | object | 否 |  | 库存信息 |  |
|     └ id | string | 否 | - | 库存唯一标识 |  |
|     └ stock | int32 | 否 | - | 库存 | 0 |
|     └ salesCount | int64 | 否 | - | 销量 | 0 |
|     └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|     └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ weight | int64 | 否 | - | 重量（kg） | 0 |
|   └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|   └ default | boolean | 否 | - | 是否默认 | true |
|   └ visible | boolean | 否 | - | 是否展示 | true |
|   └ barcode | string | 否 | - | 商品条码 |  |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
| status | int32 | 否 | - | 商品状态 ProductStatus code | 0 |
| statusDesc | string | 否 | - | 商品状态 ProductStatus desc |  |
| source | int32 | 否 | - | 商品来源 ProductSource code | 0 |
| sourceDesc | string | 否 | - | 商品来源 ProductSource desc |  |
| mainImage | string | 否 | - | 主图片 |  |
| images | array | 否 | - | 商品图片列表 | , |
| detail | string | 否 | - | 商品详情 |  |
| salesCount | int64 | 否 | - | 销量 | 0 |
| initialSalesCount | int64 | 否 | - | 初始销量 | 0 |
| createAt | int64 | 否 | - | 创建时间 | 0 |
| updateAt | int64 | 否 | - | 更新时间 | 0 |
| externalId | string | 否 | - | 外部商品ID（天猫超市/第三方商品） |  |
| delFlag | int64 | 否 | - | 是否删除 | 0 |
| description | string | 否 | - | 商品简介 |  |
| specType | enum | 否 | - | 规格类型 SpecType code<br/>(See: 规格类型枚举) | SINGLE |
| specTypeDesc | enum | 否 | - | 规格类型 SpecType desc<br/>(See: 规格类型枚举) | SINGLE |
| deliveryMethod | enum | 否 | - | 配送方式 DeliveryMethod code<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
| deliveryMethodDesc | enum | 否 | - | 配送方式 DeliveryMethod desc<br/>(See: 配送方式枚举) | MERCHANT_DELIVERY |
| checkOrderFormIdList | array | 否 | - | 下单信息表单项ID列表 | , |
| formListEmpty | boolean | 否 | - | 下单信息表单项列表是否为空的标记 | true |
| sortOrder | int32 | 否 | - | 排序 | 0 |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "productType": "PHYSICAL",
    "productTypeDesc": "PHYSICAL",
    "categoryId": "",
    "skuList": [
        {
            "id": "",
            "productId": "",
            "name": "",
            "paramList": [
                {
                    "specName": "",
                    "attrName": "",
                    "attrValue": ""
                }
            ],
            "price": 0,
            "memberPrice": 0,
            "originalPrice": 0,
            "costPrice": 0,
            "image": "",
            "inventory": {
                "id": "",
                "stock": 0,
                "salesCount": 0,
                "alertStock": 0,
                "lockedStock": 0,
                "createAt": 0,
                "updateAt": 0
            },
            "weight": 0,
            "volume": 0,
            "default": true,
            "visible": true,
            "barcode": "",
            "createAt": 0,
            "updateAt": 0
        }
    ],
    "status": 0,
    "statusDesc": "",
    "source": 0,
    "sourceDesc": "",
    "mainImage": "",
    "images": [
        0,
        0
    ],
    "detail": "",
    "salesCount": 0,
    "initialSalesCount": 0,
    "createAt": 0,
    "updateAt": 0,
    "externalId": "",
    "delFlag": 0,
    "description": "",
    "specType": "SINGLE",
    "specTypeDesc": "SINGLE",
    "deliveryMethod": "MERCHANT_DELIVERY",
    "deliveryMethodDesc": "MERCHANT_DELIVERY",
    "checkOrderFormIdList": [
        0,
        0
    ],
    "formListEmpty": true,
    "sortOrder": 0
}
```

#### 错误码

无

### 新增商品
接口权限：/store/product/create

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/add


描述：新增商品
接口权限：/store/product/create

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| name | string | 是 | - | 商品名称 |  |
| productType | int32 | 是 | - | 商品类型 | 0 |
| categoryId | string | 是 | - | 商品分类ID |  |
| skuList | array | 否 |  | 商品SKU列表 |  |
|   └ id | string | 否 | - | SKU唯一标识 |  |
|   └ productId | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | SKU 名称 |  |
|   └ paramList | array | 否 |  | SKU参数列表 |  |
|     └ specName | string | 否 | - | 商品规格名称 |  |
|     └ attrName | string | 否 | - | 商品规格属性名称 |  |
|     └ attrValue | string | 否 | - | 商品规格属性值 |  |
|   └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|   └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|   └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|   └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|   └ image | string | 否 | - | 图片 |  |
|   └ inventory | object | 否 |  | 库存信息 |  |
|     └ id | string | 否 | - | 库存唯一标识 |  |
|     └ stock | int32 | 否 | - | 库存 | 0 |
|     └ salesCount | int64 | 否 | - | 销量 | 0 |
|     └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|     └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ weight | int64 | 否 | - | 重量（kg） | 0 |
|   └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|   └ default | boolean | 否 | - | 是否默认 | true |
|   └ visible | boolean | 否 | - | 是否展示 | true |
|   └ barcode | string | 否 | - | 商品条码 |  |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
| status | int32 | 是 | - | 商品状态 | 0 |
| source | int32 | 是 | - | 商品来源 | 0 |
| mainImage | string | 否 | - | 主图片 |  |
| images | array | 否 | - | 商品图片列表 | , |
| detail | string | 否 | - | 商品详情 |  |
| description | string | 否 | - | 商品简介 |  |
| specType | int32 | 是 | - | 规格类型 | 0 |
| deliveryMethod | int32 | 是 | - | 配送方式 | 0 |
| sortOrder | int32 | 否 | - | 排序 | 0 |
| checkOrderFormIdList | array | 否 | - | 下单信息表单项ID列表 | , |
| externalId | string | 否 | - | 外部商品ID（天猫超市/第三方商品） |  |
| initialSalesCount | int64 | 否 | - | 初始销量 | 0 |

#### 请求示例

```
{
    "name": "",
    "productType": 0,
    "categoryId": "",
    "skuList": [
        {
            "id": "",
            "productId": "",
            "name": "",
            "paramList": [
                {
                    "specName": "",
                    "attrName": "",
                    "attrValue": ""
                }
            ],
            "price": 0,
            "memberPrice": 0,
            "originalPrice": 0,
            "costPrice": 0,
            "image": "",
            "inventory": {
                "id": "",
                "stock": 0,
                "salesCount": 0,
                "alertStock": 0,
                "lockedStock": 0,
                "createAt": 0,
                "updateAt": 0
            },
            "weight": 0,
            "volume": 0,
            "default": true,
            "visible": true,
            "barcode": "",
            "createAt": 0,
            "updateAt": 0
        }
    ],
    "status": 0,
    "source": 0,
    "mainImage": "",
    "images": [
        0,
        0
    ],
    "detail": "",
    "description": "",
    "specType": 0,
    "deliveryMethod": 0,
    "sortOrder": 0,
    "checkOrderFormIdList": [
        0,
        0
    ],
    "externalId": "",
    "initialSalesCount": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 更新商品
接口权限：/store/product/create

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/update


描述：更新商品
接口权限：/store/product/create

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 商品ID |  |
| name | string | 是 | - | 商品名称 |  |
| productType | int32 | 是 | - | 商品类型 | 0 |
| categoryId | string | 是 | - | 商品分类ID |  |
| skuList | array | 否 |  | 商品SKU列表 |  |
|   └ id | string | 否 | - | SKU唯一标识 |  |
|   └ productId | string | 否 | - | 商品ID |  |
|   └ name | string | 否 | - | SKU 名称 |  |
|   └ paramList | array | 否 |  | SKU参数列表 |  |
|     └ specName | string | 否 | - | 商品规格名称 |  |
|     └ attrName | string | 否 | - | 商品规格属性名称 |  |
|     └ attrValue | string | 否 | - | 商品规格属性值 |  |
|   └ price | int64 | 否 | - | 商品价格（单位：分） | 0 |
|   └ memberPrice | int64 | 否 | - | 会员价（单位：分） | 0 |
|   └ originalPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|   └ costPrice | int64 | 否 | - | 成本价（单位：分） | 0 |
|   └ image | string | 否 | - | 图片 |  |
|   └ inventory | object | 否 |  | 库存信息 |  |
|     └ id | string | 否 | - | 库存唯一标识 |  |
|     └ stock | int32 | 否 | - | 库存 | 0 |
|     └ salesCount | int64 | 否 | - | 销量 | 0 |
|     └ alertStock | int32 | 否 | - | 预警库存 | 0 |
|     └ lockedStock | int32 | 否 | - | 锁定库存（已下单但未支付） | 0 |
|     └ createAt | int64 | 否 | - | 创建时间 | 0 |
|     └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ weight | int64 | 否 | - | 重量（kg） | 0 |
|   └ volume | int64 | 否 | - | 体积（立方米） | 0 |
|   └ default | boolean | 否 | - | 是否默认 | true |
|   └ visible | boolean | 否 | - | 是否展示 | true |
|   └ barcode | string | 否 | - | 商品条码 |  |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
| mainImage | string | 否 | - | 主图片 |  |
| images | array | 否 | - | 商品图片列表 | , |
| detail | string | 否 | - | 商品详情 |  |
| description | string | 否 | - | 商品简介 |  |
| specType | int32 | 是 | - | 规格类型 | 0 |
| deliveryMethod | int32 | 是 | - | 配送方式 | 0 |
| initialSalesCount | int64 | 否 | - | 初始销量 | 0 |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "productType": 0,
    "categoryId": "",
    "skuList": [
        {
            "id": "",
            "productId": "",
            "name": "",
            "paramList": [
                {
                    "specName": "",
                    "attrName": "",
                    "attrValue": ""
                }
            ],
            "price": 0,
            "memberPrice": 0,
            "originalPrice": 0,
            "costPrice": 0,
            "image": "",
            "inventory": {
                "id": "",
                "stock": 0,
                "salesCount": 0,
                "alertStock": 0,
                "lockedStock": 0,
                "createAt": 0,
                "updateAt": 0
            },
            "weight": 0,
            "volume": 0,
            "default": true,
            "visible": true,
            "barcode": "",
            "createAt": 0,
            "updateAt": 0
        }
    ],
    "mainImage": "",
    "images": [
        0,
        0
    ],
    "detail": "",
    "description": "",
    "specType": 0,
    "deliveryMethod": 0,
    "initialSalesCount": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 更新商品状态
接口权限：/store/product/updateStatus

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/updateStatus


描述：更新商品状态
接口权限：/store/product/updateStatus

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 商品ID |  |
| status | int32 | 是 | - | 商品状态 | 0 |

#### 请求示例

```
{
    "id": "",
    "status": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 批量更新商品状态
接口权限：/store/product/batchUpdateStatus

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/batchUpdateStatus


描述：批量更新商品状态
接口权限：/store/product/batchUpdateStatus

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| ids | array | 是 | - | 商品ID列表 | , |
| status | int32 | 是 | - | 商品状态 | 0 |

#### 请求示例

```
{
    "ids": [
        0,
        0
    ],
    "status": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 删除商品
接口权限：/store/product/delete

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/delete/{id}


描述：删除商品
接口权限：/store/product/delete

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### Path参数

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| id | 是 | No comments found. |  |

#### 请求参数

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 根据分类ID统计商品数量
接口权限：/store/product/countByCategory

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/countByCategory/{id}


描述：根据分类ID统计商品数量
接口权限：/store/product/countByCategory

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### Path参数

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| id | 是 | No comments found. |  |

#### 请求参数

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 创建/编辑运费模板
接口权限：/store/shipping/create

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/createShippingFeeTpl


描述：创建/编辑运费模板
接口权限：/store/shipping/create

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 运费模版ID(编辑时指定) |  |
| name | string | 是 | - | 模版名称 |  |
| area | string | 是 | - | 模版适用地区 |  |
| shippingFee | int64 | 是 | - | 运费（单位：分） | 0 |
| remark | string | 否 | - | 备注 |  |
| sortOrder | int32 | 否 | - | 排序 | 0 |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "area": "",
    "shippingFee": 0,
    "remark": "",
    "sortOrder": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 搜索运费模板
接口权限：/store/shipping/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/searchShippingFeeTpl


描述：搜索运费模板
接口权限：/store/shipping/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| name | string | 否 | - | 模版名称（可选，用于模糊搜索） |  |
| page | int32 | 是 | - | 页码 | 0 |
| size | int32 | 是 | - | 每页大小 | 0 |

#### 请求示例

```
{
    "name": "",
    "page": 0,
    "size": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 运费模版ID |  |
|   └ name | string | 否 | - | 模版名称 |  |
|   └ area | string | 否 | - | 模版适用地区 |  |
|   └ shippingFee | int64 | 否 | - | 运费（单位：分） | 0 |
|   └ remark | string | 否 | - | 备注 |  |
|   └ sortOrder | int32 | 否 | - | 排序 | 0 |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ delFlag | int32 | 否 | - | 是否删除 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 运费模版ID |  |
|   └ name | string | 否 | - | 模版名称 |  |
|   └ area | string | 否 | - | 模版适用地区 |  |
|   └ shippingFee | int64 | 否 | - | 运费（单位：分） | 0 |
|   └ remark | string | 否 | - | 备注 |  |
|   └ sortOrder | int32 | 否 | - | 排序 | 0 |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
|   └ delFlag | int32 | 否 | - | 是否删除 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "area": "",
            "shippingFee": 0,
            "remark": "",
            "sortOrder": 0,
            "createAt": 0,
            "updateAt": 0,
            "delFlag": 0
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "area": "",
            "shippingFee": 0,
            "remark": "",
            "sortOrder": 0,
            "createAt": 0,
            "updateAt": 0,
            "delFlag": 0
        }
    ]
}
```

#### 错误码

无

### 获取所有运费模板
接口权限：/store/shipping/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/getAllShippingFeeTpl


描述：获取所有运费模板
接口权限：/store/shipping/query

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 运费模版ID |  |
| name | string | 否 | - | 模版名称 |  |
| area | string | 否 | - | 模版适用地区 |  |
| shippingFee | int64 | 否 | - | 运费（单位：分） | 0 |
| remark | string | 否 | - | 备注 |  |
| sortOrder | int32 | 否 | - | 排序 | 0 |
| createAt | int64 | 否 | - | 创建时间 | 0 |
| updateAt | int64 | 否 | - | 更新时间 | 0 |
| delFlag | int32 | 否 | - | 是否删除 | 0 |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "area": "",
    "shippingFee": 0,
    "remark": "",
    "sortOrder": 0,
    "createAt": 0,
    "updateAt": 0,
    "delFlag": 0
}
```

#### 错误码

无

### 获取运费模板详情
接口权限：/store/shipping/detail

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/getShippingFeeTplDetail/{id}


描述：获取运费模板详情
接口权限：/store/shipping/detail

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### Path参数

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| id | 是 | No comments found. |  |

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 运费模版ID |  |
| name | string | 否 | - | 模版名称 |  |
| area | string | 否 | - | 模版适用地区 |  |
| shippingFee | int64 | 否 | - | 运费（单位：分） | 0 |
| remark | string | 否 | - | 备注 |  |
| sortOrder | int32 | 否 | - | 排序 | 0 |
| createAt | int64 | 否 | - | 创建时间 | 0 |
| updateAt | int64 | 否 | - | 更新时间 | 0 |
| delFlag | int32 | 否 | - | 是否删除 | 0 |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "area": "",
    "shippingFee": 0,
    "remark": "",
    "sortOrder": 0,
    "createAt": 0,
    "updateAt": 0,
    "delFlag": 0
}
```

#### 错误码

无

### 删除运费模板
接口权限：/store/shipping/delete

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_product/deleteShippingFeeTpl/{id}


描述：删除运费模板
接口权限：/store/shipping/delete

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### Path参数

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| id | 是 | No comments found. |  |

#### 请求参数

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
