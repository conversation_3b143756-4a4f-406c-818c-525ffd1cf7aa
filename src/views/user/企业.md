# 文档

## 【后台】企业管理


### 添加/编辑企业
接口权限：/user/company/save

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_company/save


描述：添加/编辑企业
接口权限：/user/company/save

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 编辑时指定 |  |
| name | string | 是 | - | 企业名 |  |
| province | string | 是 | - | 省份名 |  |
| provinceCode | string | 是 | - | 省份编码 |  |
| city | string | 是 | - | 城市名 |  |
| cityCode | string | 是 | - | 城市编码 |  |
| available | boolean | 否 | - | 是否可以用 | true |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "province": "",
    "provinceCode": "",
    "city": "",
    "cityCode": "",
    "available": true
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 企业列表
接口权限：/user/company/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_company/search


描述：企业列表
接口权限：/user/company/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| name | string | 否 | - | 企业名 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "name": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ name | string | 否 | - | 企业名 |  |
|   └ province | string | 否 | - | 省份 |  |
|   └ provinceCode | string | 否 | - | 省份编码 |  |
|   └ city | string | 否 | - | 城市 |  |
|   └ cityCode | string | 否 | - | 省份编码 |  |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ available | boolean | 否 | - | 是否启用 | true |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ name | string | 否 | - | 企业名 |  |
|   └ province | string | 否 | - | 省份 |  |
|   └ provinceCode | string | 否 | - | 省份编码 |  |
|   └ city | string | 否 | - | 城市 |  |
|   └ cityCode | string | 否 | - | 省份编码 |  |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ available | boolean | 否 | - | 是否启用 | true |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "province": "",
            "provinceCode": "",
            "city": "",
            "cityCode": "",
            "ctime": 0,
            "available": true
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "province": "",
            "provinceCode": "",
            "city": "",
            "cityCode": "",
            "ctime": 0,
            "available": true
        }
    ]
}
```

#### 错误码

无
