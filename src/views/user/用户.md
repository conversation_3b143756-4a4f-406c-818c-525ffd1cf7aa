# 文档

## 【后台】用户管理


### 用户查询
接口权限：/user/user/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_user/search


描述：用户查询
接口权限：/user/user/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| account | string | 否 | - | 手机号/邮箱 |  |
| name | string | 否 | - | 会员名 |  |
| timeSection | object | 否 |  | 注册时间 |  |
|   └ begin | int64 | 否 | - | 开始时间(毫秒) | 0 |
|   └ end | int64 | 否 | - | 结束时间(毫秒) | 0 |
| referralName | string | 否 | - | 推广源(比如推广影院名) |  |
| companyId | string | 否 | - | 绑定企业 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "account": "",
    "name": "",
    "timeSection": {
        "begin": 0,
        "end": 0
    },
    "referralName": "",
    "companyId": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ areaCode | int32 | 否 | - | 区域码 | 0 |
|   └ channel | int32 | 否 | - | 注册渠道 | 0 |
|   └ account | string | 否 | - | phone/email |  |
|   └ name | string | 否 | - | 用户名 |  |
|   └ headImage | string | 否 | - | 头像 |  |
|   └ companys | array | 否 |  | 所属企业 |  |
|     └ id | string | 否 | - | 企业Id |  |
|     └ name | string | 否 | - | 企业名 |  |
|     └ province | string | 否 | - | 省份 |  |
|     └ provinceCode | string | 否 | - | 省份编码 |  |
|     └ city | string | 否 | - | 城市 |  |
|     └ cityCode | string | 否 | - | 省份编码 |  |
|     └ ctime | int64 | 否 | - | 创建时间 | 0 |
|     └ available | boolean | 否 | - | 是否启用 | true |
|   └ referralId | string | 否 | - | 用于搜索(推荐来源，比如影院Id) |  |
|   └ gender | int32 | 否 | - | 用户性别 | 0 |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ note | string | 否 | - | 备注 |  |
|   └ deleted | boolean | 否 | - | 账号是否已注销 | true |
|   └ lastActiveDay | int64 | 否 | - | 最后一次激活时间(应用激活时间，每天只会记录一次，不一定是最后一次使用App的时间) | 0 |
|   └ operator | string | 否 | - | 最后一次操作人 |  |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ areaCode | int32 | 否 | - | 区域码 | 0 |
|   └ channel | int32 | 否 | - | 注册渠道 | 0 |
|   └ account | string | 否 | - | phone/email |  |
|   └ name | string | 否 | - | 用户名 |  |
|   └ headImage | string | 否 | - | 头像 |  |
|   └ companys | array | 否 |  | 所属企业 |  |
|     └ id | string | 否 | - | 企业Id |  |
|     └ name | string | 否 | - | 企业名 |  |
|     └ province | string | 否 | - | 省份 |  |
|     └ provinceCode | string | 否 | - | 省份编码 |  |
|     └ city | string | 否 | - | 城市 |  |
|     └ cityCode | string | 否 | - | 省份编码 |  |
|     └ ctime | int64 | 否 | - | 创建时间 | 0 |
|     └ available | boolean | 否 | - | 是否启用 | true |
|   └ referralId | string | 否 | - | 用于搜索(推荐来源，比如影院Id) |  |
|   └ gender | int32 | 否 | - | 用户性别 | 0 |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ note | string | 否 | - | 备注 |  |
|   └ deleted | boolean | 否 | - | 账号是否已注销 | true |
|   └ lastActiveDay | int64 | 否 | - | 最后一次激活时间(应用激活时间，每天只会记录一次，不一定是最后一次使用App的时间) | 0 |
|   └ operator | string | 否 | - | 最后一次操作人 |  |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "areaCode": 0,
            "channel": 0,
            "account": "",
            "name": "",
            "headImage": "",
            "companys": [
                {
                    "id": "",
                    "name": "",
                    "province": "",
                    "provinceCode": "",
                    "city": "",
                    "cityCode": "",
                    "ctime": 0,
                    "available": true
                }
            ],
            "referralId": "",
            "gender": 0,
            "ctime": 0,
            "note": "",
            "deleted": true,
            "lastActiveDay": 0,
            "operator": ""
        }
    ],
    "result": [
        {
            "id": "",
            "areaCode": 0,
            "channel": 0,
            "account": "",
            "name": "",
            "headImage": "",
            "companys": [
                {
                    "id": "",
                    "name": "",
                    "province": "",
                    "provinceCode": "",
                    "city": "",
                    "cityCode": "",
                    "ctime": 0,
                    "available": true
                }
            ],
            "referralId": "",
            "gender": 0,
            "ctime": 0,
            "note": "",
            "deleted": true,
            "lastActiveDay": 0,
            "operator": ""
        }
    ]
}
```

#### 错误码

无

### 用户详情
接口权限：/user/user/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_user/getDetailById


描述：用户详情
接口权限：/user/user/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | id |  |

#### 请求示例

```
{
    "id": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - |  |  |
| areaCode | int32 | 否 | - | 区域码 | 0 |
| channel | int32 | 否 | - | 注册渠道 | 0 |
| account | string | 否 | - | phone/email |  |
| name | string | 否 | - | 用户名 |  |
| headImage | string | 否 | - | 头像 |  |
| companys | array | 否 |  | 所属企业 |  |
|   └ id | string | 否 | - | 企业Id |  |
|   └ name | string | 否 | - | 企业名 |  |
|   └ province | string | 否 | - | 省份 |  |
|   └ provinceCode | string | 否 | - | 省份编码 |  |
|   └ city | string | 否 | - | 城市 |  |
|   └ cityCode | string | 否 | - | 省份编码 |  |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ available | boolean | 否 | - | 是否启用 | true |
| referralId | string | 否 | - | 用于搜索(推荐来源，比如影院Id) |  |
| gender | int32 | 否 | - | 用户性别 | 0 |
| ctime | int64 | 否 | - | 创建时间 | 0 |
| note | string | 否 | - | 备注 |  |
| deleted | boolean | 否 | - | 账号是否已注销 | true |
| lastActiveDay | int64 | 否 | - | 最后一次激活时间(应用激活时间，每天只会记录一次，不一定是最后一次使用App的时间) | 0 |
| operator | string | 否 | - | 最后一次操作人 |  |
| banlance | int64 | 否 | - | 钱包余额 | 0 |
| coupons | int32 | 否 | - | 优惠券数量 | 0 |
| benefitCards | int32 | 否 | - | 光影卡数量 | 0 |
| vipCardBalance | int64 | 否 | - | 会员卡余额度 | 0 |

#### 响应示例

```
{
    "id": "",
    "areaCode": 0,
    "channel": 0,
    "account": "",
    "name": "",
    "headImage": "",
    "companys": [
        {
            "id": "",
            "name": "",
            "province": "",
            "provinceCode": "",
            "city": "",
            "cityCode": "",
            "ctime": 0,
            "available": true
        }
    ],
    "referralId": "",
    "gender": 0,
    "ctime": 0,
    "note": "",
    "deleted": true,
    "lastActiveDay": 0,
    "operator": "",
    "banlance": 0,
    "coupons": 0,
    "benefitCards": 0,
    "vipCardBalance": 0
}
```

#### 错误码

无

### 重置密码
接口权限：/user/user/resetPassword

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_user/resetPassword


描述：重置密码
接口权限：/user/user/resetPassword

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | id |  |
| password | string | 是 | - | 新密码：密码必须是password=$.MD5(srcPassword),散列值，不明文传递 |  |

#### 请求示例

```
{
    "id": "",
    "password": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 企业绑定
接口权限：/user/user/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_user/bindCompany


描述：企业绑定
接口权限：/user/user/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| ids | array | 是 | - | ids | , |
| companyId | string | 是 | - | 要绑定的企业 |  |

#### 请求示例

```
{
    "ids": [
        0,
        0
    ],
    "companyId": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 企业解绑
接口权限：/user/user/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_user/unbindCompany


描述：企业解绑
接口权限：/user/user/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| ids | array | 是 | - | ids | , |
| companyId | string | 是 | - | 要解绑的企业Id |  |

#### 请求示例

```
{
    "ids": [
        0,
        0
    ],
    "companyId": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
