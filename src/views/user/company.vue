<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { Company, CompanyListParams } from '@/api/modules/company'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { toast } from 'vue-sonner'
import companyApi from '@/api/modules/company'
import { formatDate } from '@/utils'

// 表格数据
const loading = ref(false)
const tableData = ref<Company[]>([])

// 搜索表单
const searchForm = reactive<CompanyListParams>({
  name: '',
  page: 0,
  size: 10,
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('添加企业')
const submitLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<Company>({
  name: '',
  province: '',
  provinceCode: '',
  city: '',
  cityCode: '',
  available: true,
})

// 表单验证规则
const formRules: FormRules = {
  name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  province: [{ required: true, message: '请输入省份', trigger: 'blur' }],
  provinceCode: [{ required: true, message: '请输入省份编码', trigger: 'blur' }],
  city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
  cityCode: [{ required: true, message: '请输入城市编码', trigger: 'blur' }],
}

// 获取列表数据
async function fetchData() {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current - 1,
      size: pagination.size,
    }
    const res = await companyApi.getCompanyList(params)
    const data = res.data
    tableData.value = data.content || data.result || []
    pagination.total = data.total
  }
  catch (error) {
    console.error('获取企业列表失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.current = 1
  fetchData()
}

// 重置
function handleReset() {
  searchForm.name = ''
  handleSearch()
}

// 分页
function handleSizeChange(val: number) {
  pagination.size = val
  fetchData()
}

function handleCurrentChange(val: number) {
  pagination.current = val
  fetchData()
}

// 添加
function handleAdd() {
  dialogTitle.value = '添加企业'
  formData.id = undefined
  Object.assign(formData, {
    name: '',
    province: '',
    provinceCode: '',
    city: '',
    cityCode: '',
    address: '',
    contactName: '',
    phone: '',
    email: '',
    available: true,
  })
  dialogVisible.value = true
}

// 编辑
function handleEdit(row: Company) {
  dialogTitle.value = '编辑企业'
  Object.assign(formData, row)
  dialogVisible.value = true
}

// 删除
// async function handleDelete(row: Company) {
//   try {
//     await ElMessageBox.confirm(
//       `确定要删除企业 "${row.name}" 吗？`,
//       '提示',
//       { type: 'warning' },
//     )
//     await deleteCompany(row.id!)
//     ElMessage.success('删除成功')
//     fetchData()
//   }
//   catch (error) {
//     console.error('删除失败:', error)
//   }
// }

// 提交表单
async function handleSubmit() {
  if (!formRef.value) {
    ElMessage.warning('请先填写企业信息')
    return
  }

  try {
    await formRef.value.validate()
    submitLoading.value = true
    await companyApi.saveCompany(formData)
    ElMessage.success(dialogTitle.value === '添加企业' ? '添加成功' : '更新成功')
    dialogVisible.value = false
    fetchData()
    formRef.value?.resetFields()
  }
  catch (error) {
    console.error('提交失败:', error)
  }
  finally {
    submitLoading.value = false
  }
}

// 关闭对话框
function handleDialogClose() {
  formRef.value?.resetFields()
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <div>
    <FaPageHeader title="企业管理" description="管理企业" />
    <FaPageMain>
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="企业名称">
          <el-input v-model="searchForm.name" placeholder="请输入企业名称" clearable @clear="handleSearch" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i-ep-refresh />
            重置
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <i-ep-plus />
            添加企业
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%;"
      >
        <el-table-column prop="name" label="企业名称" min-width="150" />
        <el-table-column prop="province" label="省份" width="120" />
        <el-table-column prop="city" label="城市" width="120" />

        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column prop="available" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.available ? 'success' : 'danger'">
              {{ row.available ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              编辑
            </el-button>
            <!-- <el-button type="danger" link @click="handleDelete(row)">
              删除
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </FaPageMain>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="企业名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="formData.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="省份编码" prop="provinceCode">
          <el-input v-model="formData.provinceCode" placeholder="请输入省份编码" />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="formData.city" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="城市编码" prop="cityCode">
          <el-input v-model="formData.cityCode" placeholder="请输入城市编码" />
        </el-form-item>
        <el-form-item label="状态" prop="available">
          <el-switch v-model="formData.available" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>
