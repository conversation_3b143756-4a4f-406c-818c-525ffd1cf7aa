/**
 * 优惠券模块配置管理
 * 提供统一的配置管理、环境变量处理、功能开关等
 */

// 环境类型
export enum Environment {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  STAGING = 'staging',
  PRODUCTION = 'production'
}

// 功能开关配置
export interface FeatureFlags {
  enableVirtualScroll: boolean      // 启用虚拟滚动
  enableCache: boolean              // 启用缓存
  enableLogging: boolean            // 启用日志
  enableErrorTracking: boolean      // 启用错误追踪
  enablePerformanceMonitoring: boolean // 启用性能监控
  enableBatchOperations: boolean    // 启用批量操作
  enableAdvancedSearch: boolean     // 启用高级搜索
  enableExport: boolean             // 启用导出功能
  enableImport: boolean             // 启用导入功能
  enablePreview: boolean            // 启用预览功能
}

// API配置
export interface ApiConfig {
  baseUrl: string
  timeout: number
  retryCount: number
  retryDelay: number
  enableMock: boolean
}

// 缓存配置
export interface CacheConfig {
  enabled: boolean
  duration: number                  // 缓存时长（毫秒）
  maxSize: number                   // 最大缓存条目数
  storageType: 'localStorage' | 'sessionStorage' | 'memory'
}

// 分页配置
export interface PaginationConfig {
  defaultPageSize: number
  pageSizeOptions: number[]
  maxPageSize: number
  showSizeChanger: boolean
  showQuickJumper: boolean
  showTotal: boolean
}

// 表格配置
export interface TableConfig {
  defaultHeight: number
  enableVirtualScroll: boolean
  virtualScrollThreshold: number
  enableSelection: boolean
  enableSorting: boolean
  enableFiltering: boolean
  enableResizing: boolean
}

// 表单配置
export interface FormConfig {
  enableRealTimeValidation: boolean
  validationDelay: number
  enableAutoSave: boolean
  autoSaveInterval: number
  maxFileSize: number               // 文件上传最大大小（字节）
  allowedFileTypes: string[]        // 允许的文件类型
}

// 通知配置
export interface NotificationConfig {
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  duration: number
  maxCount: number
  enableSound: boolean
  enableDesktopNotification: boolean
}

// 主配置接口
export interface CouponModuleConfig {
  environment: Environment
  version: string
  featureFlags: FeatureFlags
  api: ApiConfig
  cache: CacheConfig
  pagination: PaginationConfig
  table: TableConfig
  form: FormConfig
  notification: NotificationConfig
}

// 默认配置
const defaultConfig: CouponModuleConfig = {
  environment: Environment.DEVELOPMENT,
  version: '1.0.0',
  
  featureFlags: {
    enableVirtualScroll: true,
    enableCache: true,
    enableLogging: true,
    enableErrorTracking: true,
    enablePerformanceMonitoring: true,
    enableBatchOperations: true,
    enableAdvancedSearch: true,
    enableExport: true,
    enableImport: false,
    enablePreview: true,
  },
  
  api: {
    baseUrl: '/api',
    timeout: 30000,
    retryCount: 3,
    retryDelay: 1000,
    enableMock: false,
  },
  
  cache: {
    enabled: true,
    duration: 5 * 60 * 1000, // 5分钟
    maxSize: 100,
    storageType: 'localStorage',
  },
  
  pagination: {
    defaultPageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
    maxPageSize: 1000,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true,
  },
  
  table: {
    defaultHeight: 600,
    enableVirtualScroll: true,
    virtualScrollThreshold: 100,
    enableSelection: true,
    enableSorting: true,
    enableFiltering: true,
    enableResizing: true,
  },
  
  form: {
    enableRealTimeValidation: true,
    validationDelay: 300,
    enableAutoSave: false,
    autoSaveInterval: 30000,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['.jpg', '.jpeg', '.png', '.pdf', '.xlsx', '.csv'],
  },
  
  notification: {
    position: 'top-right',
    duration: 4000,
    maxCount: 5,
    enableSound: false,
    enableDesktopNotification: false,
  },
}

// 环境特定配置
const environmentConfigs: Record<Environment, Partial<CouponModuleConfig>> = {
  [Environment.DEVELOPMENT]: {
    featureFlags: {
      ...defaultConfig.featureFlags,
      enableLogging: true,
      enableErrorTracking: true,
      enablePerformanceMonitoring: true,
    },
    api: {
      ...defaultConfig.api,
      enableMock: true,
    },
  },
  
  [Environment.TESTING]: {
    featureFlags: {
      ...defaultConfig.featureFlags,
      enableLogging: true,
      enableErrorTracking: true,
    },
    api: {
      ...defaultConfig.api,
      timeout: 10000,
    },
  },
  
  [Environment.STAGING]: {
    featureFlags: {
      ...defaultConfig.featureFlags,
      enableLogging: true,
      enableErrorTracking: true,
      enableImport: true,
    },
  },
  
  [Environment.PRODUCTION]: {
    featureFlags: {
      ...defaultConfig.featureFlags,
      enableLogging: false,
      enableErrorTracking: true,
      enablePerformanceMonitoring: false,
      enableImport: true,
    },
    cache: {
      ...defaultConfig.cache,
      duration: 10 * 60 * 1000, // 10分钟
    },
  },
}

class ConfigManager {
  private config: CouponModuleConfig
  private listeners: Array<(config: CouponModuleConfig) => void> = []

  constructor() {
    this.config = this.loadConfig()
  }

  /**
   * 加载配置
   */
  private loadConfig(): CouponModuleConfig {
    const environment = this.getEnvironment()
    const envConfig = environmentConfigs[environment] || {}
    
    // 合并默认配置和环境配置
    const config = this.deepMerge(defaultConfig, envConfig) as CouponModuleConfig
    
    // 从本地存储加载用户自定义配置
    const userConfig = this.loadUserConfig()
    if (userConfig) {
      return this.deepMerge(config, userConfig) as CouponModuleConfig
    }
    
    return config
  }

  /**
   * 获取当前环境
   */
  private getEnvironment(): Environment {
    const env = process.env.NODE_ENV || 'development'
    return env as Environment
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target }
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
    
    return result
  }

  /**
   * 从本地存储加载用户配置
   */
  private loadUserConfig(): Partial<CouponModuleConfig> | null {
    try {
      const stored = localStorage.getItem('coupon_module_config')
      return stored ? JSON.parse(stored) : null
    } catch {
      return null
    }
  }

  /**
   * 保存用户配置到本地存储
   */
  private saveUserConfig(config: Partial<CouponModuleConfig>) {
    try {
      localStorage.setItem('coupon_module_config', JSON.stringify(config))
    } catch (error) {
      console.error('Failed to save user config:', error)
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): CouponModuleConfig {
    return { ...this.config }
  }

  /**
   * 获取特定配置项
   */
  get<K extends keyof CouponModuleConfig>(key: K): CouponModuleConfig[K] {
    return this.config[key]
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<CouponModuleConfig>) {
    const newConfig = this.deepMerge(this.config, updates) as CouponModuleConfig
    this.config = newConfig
    
    // 保存到本地存储
    this.saveUserConfig(updates)
    
    // 通知监听器
    this.notifyListeners()
  }

  /**
   * 重置配置
   */
  resetConfig() {
    this.config = this.loadConfig()
    localStorage.removeItem('coupon_module_config')
    this.notifyListeners()
  }

  /**
   * 检查功能是否启用
   */
  isFeatureEnabled(feature: keyof FeatureFlags): boolean {
    return this.config.featureFlags[feature]
  }

  /**
   * 切换功能开关
   */
  toggleFeature(feature: keyof FeatureFlags) {
    this.updateConfig({
      featureFlags: {
        ...this.config.featureFlags,
        [feature]: !this.config.featureFlags[feature]
      }
    })
  }

  /**
   * 添加配置变更监听器
   */
  addListener(listener: (config: CouponModuleConfig) => void) {
    this.listeners.push(listener)
  }

  /**
   * 移除配置变更监听器
   */
  removeListener(listener: (config: CouponModuleConfig) => void) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.config)
      } catch (error) {
        console.error('Config listener error:', error)
      }
    })
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson)
      this.updateConfig(config)
      return true
    } catch {
      return false
    }
  }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager()

// 便捷方法
export const getConfig = () => configManager.getConfig()
export const isFeatureEnabled = (feature: keyof FeatureFlags) => configManager.isFeatureEnabled(feature)
export const updateConfig = (updates: Partial<CouponModuleConfig>) => configManager.updateConfig(updates)
