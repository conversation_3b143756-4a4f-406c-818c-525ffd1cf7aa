/**
 * 日志记录系统
 * 提供结构化日志记录、性能监控、用户行为追踪等功能
 */

// 日志级别
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

// 日志类型
export enum LogType {
  USER_ACTION = 'USER_ACTION', // 用户操作
  API_CALL = 'API_CALL', // API调用
  PERFORMANCE = 'PERFORMANCE', // 性能监控
  ERROR = 'ERROR', // 错误日志
  BUSINESS = 'BUSINESS', // 业务日志
}

// 日志条目接口
export interface LogEntry {
  id: string
  timestamp: number
  level: LogLevel
  type: LogType
  message: string
  data?: Record<string, any>
  userId?: string
  sessionId?: string
  module: string
  action?: string
  duration?: number
  url?: string
  userAgent?: string
}

// 日志配置
interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableStorage: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  maxLocalLogs: number
  batchSize: number
  flushInterval: number
}

class Logger {
  private config: LoggerConfig = {
    level: LogLevel.INFO,
    enableConsole: true,
    enableStorage: true,
    enableRemote: false,
    maxLocalLogs: 1000,
    batchSize: 10,
    flushInterval: 30000, // 30秒
  }

  private logQueue: LogEntry[] = []
  private sessionId: string
  private flushTimer?: number

  constructor() {
    this.sessionId = this.generateSessionId()
    this.startFlushTimer()
  }

  /**
   * 配置日志器
   */
  configure(config: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...config }

    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.startFlushTimer()
  }

  /**
   * 记录调试日志
   */
  debug(message: string, data?: Record<string, any>, action?: string) {
    this.log(LogLevel.DEBUG, LogType.BUSINESS, message, data, action)
  }

  /**
   * 记录信息日志
   */
  info(message: string, data?: Record<string, any>, action?: string) {
    this.log(LogLevel.INFO, LogType.BUSINESS, message, data, action)
  }

  /**
   * 记录警告日志
   */
  warn(message: string, data?: Record<string, any>, action?: string) {
    this.log(LogLevel.WARN, LogType.BUSINESS, message, data, action)
  }

  /**
   * 记录错误日志
   */
  error(message: string, data?: Record<string, any>, action?: string) {
    this.log(LogLevel.ERROR, LogType.ERROR, message, data, action)
  }

  /**
   * 记录用户操作
   */
  logUserAction(action: string, data?: Record<string, any>) {
    this.log(LogLevel.INFO, LogType.USER_ACTION, `用户操作: ${action}`, data, action)
  }

  /**
   * 记录API调用
   */
  logApiCall(method: string, url: string, duration: number, status?: number, data?: Record<string, any>) {
    this.log(LogLevel.INFO, LogType.API_CALL, `API调用: ${method} ${url}`, {
      method,
      url,
      status,
      duration,
      ...data,
    }, 'api_call')
  }

  /**
   * 记录性能指标
   */
  logPerformance(metric: string, value: number, data?: Record<string, any>) {
    this.log(LogLevel.INFO, LogType.PERFORMANCE, `性能指标: ${metric}`, {
      metric,
      value,
      ...data,
    }, 'performance')
  }

  /**
   * 核心日志记录方法
   */
  private log(level: LogLevel, type: LogType, message: string, data?: Record<string, any>, action?: string) {
    // 检查日志级别
    if (level < this.config.level) {
      return
    }

    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: Date.now(),
      level,
      type,
      message,
      data,
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      module: 'coupon',
      action,
      url: window.location.href,
      userAgent: navigator.userAgent,
    }

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(logEntry)
    }

    // 本地存储
    if (this.config.enableStorage) {
      this.logToStorage(logEntry)
    }

    // 远程发送
    if (this.config.enableRemote) {
      this.logQueue.push(logEntry)

      if (this.logQueue.length >= this.config.batchSize) {
        this.flushLogs()
      }
    }
  }

  /**
   * 控制台输出
   */
  private logToConsole(entry: LogEntry) {
    const { level, message, data } = entry
    const prefix = `[${new Date(entry.timestamp).toISOString()}] [${LogLevel[level]}] [${entry.type}]`

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(prefix, message, data)
        break
      case LogLevel.INFO:
        console.info(prefix, message, data)
        break
      case LogLevel.WARN:
        console.warn(prefix, message, data)
        break
      case LogLevel.ERROR:
        console.error(prefix, message, data)
        break
    }
  }

  /**
   * 本地存储
   */
  private logToStorage(entry: LogEntry) {
    try {
      const logs = this.getStoredLogs()
      logs.push(entry)

      // 限制日志数量
      if (logs.length > this.config.maxLocalLogs) {
        logs.splice(0, logs.length - this.config.maxLocalLogs)
      }

      localStorage.setItem('app_logs', JSON.stringify(logs))
    }
    catch (error) {
      console.error('Failed to store log:', error)
    }
  }

  /**
   * 批量发送日志到远程服务器
   */
  private async flushLogs() {
    if (this.logQueue.length === 0 || !this.config.remoteEndpoint) {
      return
    }

    const logsToSend = [...this.logQueue]
    this.logQueue = []

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          logs: logsToSend,
          timestamp: Date.now(),
        }),
      })
    }
    catch (error) {
      console.error('Failed to send logs to remote server:', error)
      // 发送失败时重新加入队列
      this.logQueue.unshift(...logsToSend)
    }
  }

  /**
   * 获取存储的日志
   */
  private getStoredLogs(): LogEntry[] {
    try {
      const logs = localStorage.getItem('app_logs')
      return logs ? JSON.parse(logs) : []
    }
    catch {
      return []
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    // 这里应该从用户状态管理中获取用户ID
    // 暂时返回undefined
    return undefined
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer() {
    if (this.config.enableRemote && this.config.flushInterval > 0) {
      this.flushTimer = window.setInterval(() => {
        this.flushLogs()
      }, this.config.flushInterval)
    }
  }

  /**
   * 获取日志统计
   */
  getLogStats(): Record<string, number> {
    const logs = this.getStoredLogs()
    const stats: Record<string, number> = {}

    logs.forEach((log) => {
      const key = `${LogType[log.type]}_${LogLevel[log.level]}`
      stats[key] = (stats[key] || 0) + 1
    })

    return stats
  }

  /**
   * 清除本地日志
   */
  clearLogs() {
    localStorage.removeItem('app_logs')
    this.logQueue = []
  }

  /**
   * 导出日志
   */
  exportLogs(): LogEntry[] {
    return this.getStoredLogs()
  }

  /**
   * 销毁日志器
   */
  destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flushLogs() // 最后一次刷新
  }
}

// 创建全局日志器实例
export const logger = new Logger()

// 便捷方法
export function logUserAction(action: string, data?: Record<string, any>) {
  logger.logUserAction(action, data)
}

export function logApiCall(method: string, url: string, duration: number, status?: number, data?: Record<string, any>) {
  logger.logApiCall(method, url, duration, status, data)
}

export function logPerformance(metric: string, value: number, data?: Record<string, any>) {
  logger.logPerformance(metric, value, data)
}

// 性能监控装饰器
export function logExecutionTime(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value

  descriptor.value = async function (...args: any[]) {
    const startTime = performance.now()

    try {
      const result = await method.apply(this, args)
      const duration = performance.now() - startTime

      logger.logPerformance(`${target.constructor.name}.${propertyName}`, duration, {
        args: args.length,
        success: true,
      })

      return result
    }
    catch (error) {
      const duration = performance.now() - startTime

      logger.logPerformance(`${target.constructor.name}.${propertyName}`, duration, {
        args: args.length,
        success: false,
        error: error instanceof Error ? error.message : String(error),
      })

      throw error
    }
  }

  return descriptor
}
