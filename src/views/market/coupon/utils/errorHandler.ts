/**
 * 统一错误处理工具
 * 提供错误分类、日志记录、用户友好提示等功能
 */

import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',           // 网络错误
  API = 'API',                   // API错误
  VALIDATION = 'VALIDATION',     // 验证错误
  PERMISSION = 'PERMISSION',     // 权限错误
  BUSINESS = 'BUSINESS',         // 业务逻辑错误
  UNKNOWN = 'UNKNOWN'            // 未知错误
}

// 错误级别
export enum ErrorLevel {
  LOW = 'LOW',       // 低级别：不影响核心功能
  MEDIUM = 'MEDIUM', // 中级别：影响部分功能
  HIGH = 'HIGH',     // 高级别：影响核心功能
  CRITICAL = 'CRITICAL' // 严重：系统不可用
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  level: ErrorLevel
  code?: string | number
  message: string
  originalError?: any
  context?: Record<string, any>
  timestamp?: number
  userId?: string
  userAgent?: string
  url?: string
}

// 错误处理配置
interface ErrorHandlerConfig {
  enableLogging: boolean
  enableNotification: boolean
  enableConsoleLog: boolean
  logEndpoint?: string
  maxRetries: number
  retryDelay: number
}

class ErrorHandler {
  private config: ErrorHandlerConfig = {
    enableLogging: true,
    enableNotification: true,
    enableConsoleLog: true,
    maxRetries: 3,
    retryDelay: 1000,
  }

  private errorMessages: Record<string, string> = {
    // 网络错误
    'NETWORK_TIMEOUT': '网络请求超时，请检查网络连接',
    'NETWORK_ERROR': '网络连接失败，请稍后重试',
    'NETWORK_OFFLINE': '网络连接已断开，请检查网络设置',
    
    // API错误
    'API_NOT_FOUND': '请求的资源不存在',
    'API_UNAUTHORIZED': '登录已过期，请重新登录',
    'API_FORBIDDEN': '没有权限执行此操作',
    'API_SERVER_ERROR': '服务器内部错误，请稍后重试',
    'API_BAD_REQUEST': '请求参数错误',
    
    // 业务错误
    'COUPON_NOT_FOUND': '优惠券不存在或已被删除',
    'COUPON_EXPIRED': '优惠券已过期',
    'COUPON_USED': '优惠券已被使用',
    'COUPON_LIMIT_EXCEEDED': '优惠券使用次数已达上限',
    
    // 验证错误
    'VALIDATION_REQUIRED': '必填字段不能为空',
    'VALIDATION_FORMAT': '字段格式不正确',
    'VALIDATION_LENGTH': '字段长度不符合要求',
    
    // 默认错误
    'DEFAULT': '操作失败，请稍后重试'
  }

  /**
   * 设置错误处理配置
   */
  configure(config: Partial<ErrorHandlerConfig>) {
    this.config = { ...this.config, ...config }
  }

  /**
   * 处理错误
   */
  handle(error: any, context?: Record<string, any>): ErrorInfo {
    const errorInfo = this.parseError(error, context)
    
    // 记录错误日志
    if (this.config.enableLogging) {
      this.logError(errorInfo)
    }
    
    // 显示用户通知
    if (this.config.enableNotification) {
      this.showNotification(errorInfo)
    }
    
    // 控制台输出
    if (this.config.enableConsoleLog) {
      console.error('Error handled:', errorInfo)
    }
    
    return errorInfo
  }

  /**
   * 解析错误信息
   */
  private parseError(error: any, context?: Record<string, any>): ErrorInfo {
    const timestamp = Date.now()
    const url = window.location.href
    const userAgent = navigator.userAgent
    
    // 网络错误
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      return {
        type: ErrorType.NETWORK,
        level: ErrorLevel.MEDIUM,
        code: error.code || 'NETWORK_ERROR',
        message: this.getErrorMessage('NETWORK_ERROR'),
        originalError: error,
        context,
        timestamp,
        url,
        userAgent
      }
    }
    
    // HTTP状态码错误
    if (error.response?.status) {
      const status = error.response.status
      let type = ErrorType.API
      let level = ErrorLevel.MEDIUM
      let messageKey = 'DEFAULT'
      
      switch (status) {
        case 400:
          messageKey = 'API_BAD_REQUEST'
          level = ErrorLevel.LOW
          break
        case 401:
          messageKey = 'API_UNAUTHORIZED'
          level = ErrorLevel.HIGH
          break
        case 403:
          messageKey = 'API_FORBIDDEN'
          level = ErrorLevel.HIGH
          break
        case 404:
          messageKey = 'API_NOT_FOUND'
          level = ErrorLevel.LOW
          break
        case 500:
        case 502:
        case 503:
        case 504:
          messageKey = 'API_SERVER_ERROR'
          level = ErrorLevel.HIGH
          break
      }
      
      return {
        type,
        level,
        code: status,
        message: error.response.data?.msg || this.getErrorMessage(messageKey),
        originalError: error,
        context,
        timestamp,
        url,
        userAgent
      }
    }
    
    // 业务逻辑错误
    if (error.code && this.errorMessages[error.code]) {
      return {
        type: ErrorType.BUSINESS,
        level: ErrorLevel.MEDIUM,
        code: error.code,
        message: this.getErrorMessage(error.code),
        originalError: error,
        context,
        timestamp,
        url,
        userAgent
      }
    }
    
    // 验证错误
    if (error.name === 'ValidationError') {
      return {
        type: ErrorType.VALIDATION,
        level: ErrorLevel.LOW,
        code: 'VALIDATION_ERROR',
        message: error.message || this.getErrorMessage('VALIDATION_REQUIRED'),
        originalError: error,
        context,
        timestamp,
        url,
        userAgent
      }
    }
    
    // 未知错误
    return {
      type: ErrorType.UNKNOWN,
      level: ErrorLevel.MEDIUM,
      code: 'UNKNOWN_ERROR',
      message: error.message || this.getErrorMessage('DEFAULT'),
      originalError: error,
      context,
      timestamp,
      url,
      userAgent
    }
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(key: string): string {
    return this.errorMessages[key] || this.errorMessages['DEFAULT']
  }

  /**
   * 记录错误日志
   */
  private async logError(errorInfo: ErrorInfo) {
    try {
      // 发送到日志服务
      if (this.config.logEndpoint) {
        await fetch(this.config.logEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(errorInfo)
        })
      }
      
      // 本地存储
      const logs = this.getLocalLogs()
      logs.push(errorInfo)
      
      // 只保留最近100条日志
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100)
      }
      
      localStorage.setItem('error_logs', JSON.stringify(logs))
    } catch (logError) {
      console.error('Failed to log error:', logError)
    }
  }

  /**
   * 显示用户通知
   */
  private showNotification(errorInfo: ErrorInfo) {
    const { level, message } = errorInfo
    
    switch (level) {
      case ErrorLevel.LOW:
        ElMessage({
          type: 'warning',
          message,
          duration: 3000
        })
        break
        
      case ErrorLevel.MEDIUM:
        ElMessage({
          type: 'error',
          message,
          duration: 4000
        })
        break
        
      case ErrorLevel.HIGH:
      case ErrorLevel.CRITICAL:
        ElNotification({
          type: 'error',
          title: '系统错误',
          message,
          duration: 6000
        })
        break
    }
  }

  /**
   * 获取本地日志
   */
  private getLocalLogs(): ErrorInfo[] {
    try {
      const logs = localStorage.getItem('error_logs')
      return logs ? JSON.parse(logs) : []
    } catch {
      return []
    }
  }

  /**
   * 清除本地日志
   */
  clearLocalLogs() {
    localStorage.removeItem('error_logs')
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): Record<string, number> {
    const logs = this.getLocalLogs()
    const stats: Record<string, number> = {}
    
    logs.forEach(log => {
      const key = `${log.type}_${log.level}`
      stats[key] = (stats[key] || 0) + 1
    })
    
    return stats
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 便捷方法
export const handleError = (error: any, context?: Record<string, any>) => {
  return errorHandler.handle(error, context)
}

export const handleApiError = (error: any, operation?: string) => {
  return errorHandler.handle(error, { operation, module: 'coupon' })
}

export const handleValidationError = (error: any, field?: string) => {
  return errorHandler.handle(error, { field, type: 'validation' })
}
