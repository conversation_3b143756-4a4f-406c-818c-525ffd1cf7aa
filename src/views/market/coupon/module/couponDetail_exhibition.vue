<script setup>
import { getCurrentInstance, reactive } from 'vue'

const props = defineProps({
  couponDetail: {
    type: Object,
    default: null,
    request: true,
  },
  cinemaList: {
    type: Function,
    default: null,
    request: true,
  },
})
const { proxy } = getCurrentInstance()
const { bus_film_type } = proxy.useDict('bus_film_type')

// useOn枚举值：0-电影票，1-卖品，2-演出票, 3-展览票
const useOn = reactive({
  0: '电影票',
  1: '卖品',
  2: '演出票',
  3: '演出票',
})
const dayNames = reactive({
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
})

function getHallByIds(hallId) {
  // return cinemaHallList.value.find(f => f.id === hallId)
  return `${hallId}AAA`
}

function groupBy(list, key) {
  return list.reduce((map, item) => {
    const k = item[key]
    const collection = map[k] || []
    collection.push(item)
    map[k] = collection
    return map
  }, {})
}
</script>

<template>
  <!-- 适用条件-电影票 -->
  <el-card v-if="couponDetail.couponMainInfo.useOn === 3" shadow="never" style="margin-top: 24px;">
    <template #header>
      <el-text size="large">
        {{ useOn[couponDetail.couponMainInfo.useOn] }}适用条件
      </el-text>
    </template>
    <el-descriptions
      v-for="(ticketConfig, index) in couponDetail.couponExhibitionConfigs" :key="ticketConfig.couponExhibitionSelectScopeConfig.id"
      :column="1"
      :title="`适用条件${index + 1}`"
    >
      <el-descriptions-item label="展馆限制类型：" label-align="right" label-class-name="my-label">
        <el-tag v-if="ticketConfig.couponExhibitionSelectScopeConfig.cinemaScope === 0" type="success">
          不限展馆
        </el-tag>
        <el-tag v-else-if="ticketConfig.couponExhibitionSelectScopeConfig.cinemaScope === 1" type="success">
          指定展馆
        </el-tag>
        <el-tag v-else-if="ticketConfig.couponExhibitionSelectScopeConfig.cinemaScope === 2" type="danger">
          排除展馆
        </el-tag>
        <!-- 展馆范围：全部0，指定1，排除2 -->
      </el-descriptions-item>

      <el-descriptions-item
        v-if="ticketConfig.couponExhibitionSelectScopeConfig.cinemaScope > 0 && ticketConfig.couponCinemas.length
        " label="影厅范围：" label-align="right" label-class-name="my-label"
      >
        <div style="display: inline-block; width: 70%;">
          <div>
            显示的影厅{{
              ticketConfig.couponExhibitionSelectScopeConfig.cinemaScope === 1 ? "可" : "不可"
            }}用此优惠券
          </div>
          <el-card style="margin-top: 12px;">
            <template v-for="cinemas in groupBy(ticketConfig.couponCinemas, 'cinemaName')" :key="cinemas.cinemaId">
              <div style="display: flex; margin-top: 8px;">
                <!--                {{cinemas}} -->
                <div class="hall-label">
                  {{ cinemas[0].cinemaName }}:
                  <!--                    {{cinemas}} -->
                  <el-tag
                    v-if="cinemas.length === 1 && cinemas[0].hallId === null"
                    type="success"
                  >
                    不限影厅
                  </el-tag>
                  <el-tag
                    v-for="hall in cinemas" v-else-if="cinemas.length > 0" :key="hall.cinemaId"
                    style="margin: 2px;" type="primary"
                  >
                    {{ hall.hallName }}
                    <!-- {{ hall.hallName || hall.hallId }} -->
                    <!--                      {{ getHallByIds(hall.hallId)?.hallName }} -->
                  </el-tag>
                </div>
              </div>
            </template>
          </el-card>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="适用影片类型：" label-align="right" label-class-name="my-label">
        <!-- 适用影片：全部影片0，指定影片1，最低价影片2 -->
        <span v-if="ticketConfig.couponExhibitionSelectScopeConfig.filmScope === 0">不限展览</span>
        <span v-else-if="ticketConfig.couponExhibitionSelectScopeConfig.filmScope === 1">指定展览</span>
        <!--        <span v-else-if="ticketConfig.couponExhibitionSelectScopeConfig.filmScope === 2">最低价影片</span> -->
      </el-descriptions-item>
      <el-descriptions-item
        v-if="ticketConfig.couponExhibitionSelectScopeConfig.filmScope > 0" label="适用影片：" label-align="right"
        label-class-name="my-label"
      >
        <span style="display: inline-block; width: 70%;">
          <el-tag
            v-for="item in ticketConfig.couponFilms" :key="item.filmId"
            style="margin-right: 12px; margin-bottom: 8px;" type="success"
          >{{
            item.filmName || item.filmId
          }}</el-tag>
        </span>
      </el-descriptions-item>
      <el-descriptions-item
        v-if="couponDetail.couponMainInfo.useOn === 0" label="适用影片版本：" label-align="right"
        label-class-name="my-label"
      >
        <span v-for="(item, index) in couponDetail.couponExhibitionConfigs" :key="index">
          <!-- <pre>{{ item.couponFilmVersions }}</pre> -->
          <el-text v-if="item.couponFilmVersions.length === 0" style="display: inline-block; width: 70%;">
            不限版本
          </el-text>
          <el-text v-else style="display: inline-block; width: 70%;">
            <el-tag v-for="(item, index) in item.couponFilmVersions" :key="index" style="margin-right: 8px;">
              {{ bus_film_type.find((i) => i.value === item.filmVersion).label }}
            </el-tag>
          </el-text>
        </span>
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>
