<script setup>
import { defineProps, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: {
      validityScope: 0, // 有效期方式0.绑定后N天可用 1.指定日期可用
      overdueDay: 1, // 绑定后N天可用
      validityRange: [], // 指定券可用日期
    },
  },
})
const emit = defineEmits(['update:modelValue'])
watch(() => props.modelValue, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })
</script>

<template>
  <div>
    <el-divider content-position="left">
      <span>有效期</span>
    </el-divider>
    <el-form-item label="有效期方式">
      <el-radio-group v-model="modelValue.validityScope">
        <el-radio :value="1">
          绑定后N天可用
        </el-radio>
        <el-radio :value="0">
          指定时间段可用
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="modelValue.validityScope === 1" label="绑定后N天可用" prop="validity">
      <el-text>绑定后</el-text>
      <el-input-number
        v-model="modelValue.overdueDay" :min="1" :max="99999999" :step="1" controls-position="right"
        placeholder="请输入天数"
      />
      <span>天内可用</span>
    </el-form-item>
    <el-form-item v-if="modelValue.validityScope === 0" label="有效期" prop="validityRange">
      <el-date-picker
        v-model="modelValue.validityRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </el-form-item>
  </div>
</template>

<style scoped>
/* 添加样式 */
</style>
