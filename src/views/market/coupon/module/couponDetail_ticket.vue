<script setup>
import { getCurrentInstance, reactive } from 'vue'

const props = defineProps({
  couponDetail: {
    type: Object,
    default: null,
    request: true,
  },
  cinemaList: {
    type: Function,
    default: null,
    request: true,
  },
  cinemaHallList: {
    type: Array,
    default: [],
    request: true,
  },
})
const { proxy } = getCurrentInstance()
const { bus_film_type } = proxy.useDict('bus_film_type')

// useOn枚举值：0-电影票，1-卖品，2-演出票
const useOn = reactive({
  0: '电影票',
  1: '卖品',
  2: '演出票',
  3: '演出票',
})
const dayNames = reactive({
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
})

function getCinemaById(cinemaId) {
  if (!props.cinemaHallList || !cinemaId) { return null }
  return props.cinemaHallList.find(item => item.id === cinemaId)
}

function getHallByIds(cinemaId, hallId) {
  const cinema = getCinemaById(cinemaId)
  if (!cinema || !cinema.hallList) { return null }
  return cinema.hallList.find(item => item.id === hallId)
}

// 安全检查函数
function safeGet(obj, path, defaultValue = null) {
  if (!obj) { return defaultValue }
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : defaultValue
  }, obj)
}

// 过滤有效的票券配置
const validTicketConfigs = computed(() => {
  return props.couponDetail?.couponTicketConfigs?.filter(config => config) || []
})
</script>

<template>
  <!-- 适用条件-电影票 -->
  <el-card style="margin-top: 24px;" shadow="never">
    <template #header>
      <el-text size="large">
        {{ useOn[couponDetail.couponMainInfo?.useOn] }}适用条件
      </el-text>
    </template>
    <el-empty v-if="!validTicketConfigs.length" description="暂无适用条件配置" />
    <el-descriptions
      v-for="(ticketConfig, index) in validTicketConfigs" :key="ticketConfig.couponTicketSelectScopeConfig?.id || index"
      :title="`适用条件${index + 1}`"
      :column="1"
      style=" padding: 1rem; margin-bottom: 1rem;border: 1px solid #e8d7df;"
    >
      <el-descriptions-item label-align="right" label-class-name="my-label" label="影院限制类型：">
        <el-tag v-if="ticketConfig.couponTicketSelectScopeConfig?.cinemaScope === 0" type="success">
          不限影院
        </el-tag>
        <el-tag v-else-if="ticketConfig.couponTicketSelectScopeConfig?.cinemaScope === 1" type="success">
          指定影院
        </el-tag>
        <el-tag v-else-if="ticketConfig.couponTicketSelectScopeConfig?.cinemaScope === 2" type="danger">
          排除影院
        </el-tag>
        <!-- 影院范围：全部0，指定1，排除2 -->
      </el-descriptions-item>
      <el-descriptions-item
        v-if="ticketConfig.couponTicketSelectScopeConfig?.cinemaScope > 0
          && ticketConfig.couponCinemas?.length
          && couponDetail.couponMainInfo.useOn === 0
        " label-align="right" label-class-name="my-label" label="影厅范围："
      >
        <div style="display: inline-block; width: 70%;">
          <div>
            显示的影厅{{
              ticketConfig.couponTicketSelectScopeConfig?.cinemaScope === 1 ? "可" : "不可"
            }}用此优惠券
          </div>
          <el-card style="margin-top: 12px;">
            <template v-for="cinema in cinemaList(ticketConfig.couponCinemas)" :key="cinema.cinemaId">
              <div style="display: flex; margin-top: 8px;">
                <div class="hall-label">
                  {{ cinema.cinemaName }}
                </div>
                <template v-if="couponDetail.couponMainInfo.useOn === 0">
                  <!-- {{ cinema }} -->
                  <el-row>
                    <el-tag
                      v-if="cinema.hallList && cinema.hallList.length === 1 && cinema.hallList[0]?.hallId === null"
                      type="success"
                    >
                      不限影厅
                    </el-tag>
                    <el-tag
                      v-for="hall in cinema.hallList" v-else :key="hall?.cinemaId"
                      style="margin: 2px;" type="primary"
                    >
                      <!-- {{ hall.hallName || hall.hallId }} -->
                      <!-- {{ getHallByIds(hall.hallId)?.hallName }} -->
                      <!-- {{ getHallByIds(hall.cinemaId, hall.hallId).hallName || hall.hallId }} -->
                      {{ getHallByIds(hall.cinemaId, hall.hallId)?.hallName || hall.hallId }}
                      <!-- {{ hall.hallId }} -->
                    </el-tag>
                  </el-row>
                </template>
              </div>
            </template>
          </el-card>
        </div>
      </el-descriptions-item>
      <!-- 适用时间段 -->
      <el-descriptions-item
        v-if="couponDetail.couponMainInfo.useOn === 1" label-align="right"
        label-class-name="my-label" label="适用时间类型："
      >
        <!-- 适用时段：全部时段0，指定时段1 -->
        {{ ticketConfig.couponTicketSelectScopeConfig?.periodScope === 0 ? "全部时段" : "指定时段" }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="couponDetail.couponMainInfo.useOn === 0
          && ticketConfig.couponTicketSelectScopeConfig?.periodScope === 1" label-align="right" label-class-name="my-label"
        label="适用时间段："
      >
        <!-- 适用时段：全部时段0，指定时段1 -->
        <div style="display: inline-block; width: 70%;">
          <el-tag v-for="item in ticketConfig.couponPeriods" :key="item?.id || index" style="margin: 2px;">
            {{ dayNames[item?.day] }} {{ item?.start }}-{{ item?.end }}
          </el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item
        v-if="couponDetail.couponMainInfo.useOn === 0" label-align="right" label-class-name="my-label"
        label="适用影片类型："
      >
        <!-- 适用影片：全部影片0，指定影片1，最低价影片2 -->
        <span v-if="ticketConfig.couponTicketSelectScopeConfig?.filmScope === 0">不限影片</span>
        <span v-else-if="ticketConfig.couponTicketSelectScopeConfig?.filmScope === 1">指定影片</span>
        <span v-else-if="ticketConfig.couponTicketSelectScopeConfig?.filmScope === 2">最低价影片</span>
      </el-descriptions-item>
      <el-descriptions-item
        v-if="couponDetail.couponMainInfo.useOn === 0" label-align="right" label-class-name="my-label"
        label="适用影片："
      >
        <span style="display: inline-block; width: 70%;">
          <div
            v-if="couponDetail.couponMainInfo.useOn === 0
              && ticketConfig.couponTicketSelectScopeConfig?.filmScope === 0
            "
          >
            不限影片
          </div>
          <div
            v-else-if="couponDetail.couponMainInfo.useOn === 0
              && ticketConfig.couponTicketSelectScopeConfig?.filmScope === 1
            "
          >
            <el-tag
              v-for="item in ticketConfig.couponFilms" :key="item?.filmId"
              style="margin-right: 12px; margin-bottom: 8px;" type="success"
            >{{
              item?.filmName
            }}</el-tag>
          </div>
          <div
            v-else-if="couponDetail.couponMainInfo.useOn === 0
              && ticketConfig.couponTicketSelectScopeConfig?.filmScope === 2
            "
          >
            指定最低限价影片，适用最低限价影片 {{ ticketConfig.couponTicketSelectScopeConfig?.lowestPrice }}
          </div>
        </span>
      </el-descriptions-item>
      <el-descriptions-item
        v-if="couponDetail.couponMainInfo.useOn === 0" label-align="right" label-class-name="my-label"
        label="适用影片版本："
      >
        <el-text v-if="ticketConfig.couponFilmVersions?.length === 0" style="display: inline-block; width: 70%;">
          不限版本
        </el-text>
        <span v-for="(item, index) in ticketConfig.couponFilmVersions" v-else :key="index">
          <!-- <pre>{{ item.couponFilmVersions }}</pre> -->
          {{ bus_film_type.find((i) => i.value === item.filmVersion)?.label || item }}
          <!-- <el-text v-if="item.couponFilmVersions.length === 0" style="display: inline-block; width: 70%">
            不限版本
          </el-text>
          <el-text v-else style="display: inline-block; width: 70%">
            <el-tag v-for="(item, index) in item.couponFilmVersions" style="margin-right: 8px" :key="index">
              {{ bus_film_type.find((i) => i.value === item.filmVersion).label }}
            </el-tag>
          </el-text> -->
        </span>
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>
