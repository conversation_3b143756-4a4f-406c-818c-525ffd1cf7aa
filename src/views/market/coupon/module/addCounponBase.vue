<script setup>
import { InfoFilled } from '@element-plus/icons-vue'
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
import { createCoupon, changeCouponStatus } from '@/api/modules/market/coupon'
// import { getAllChannel } from '@/api/market/channel'
import {
  CHANNEL_SCOPE,
  COUPON_TYPE_OPTIONS,
  COUPON_TYPE_SCOPE,
  COUPON_TYPES,
  DEFAULT_VALUES,
  EXCHANGE_DIFF_TYPE,
  INPUT_LIMITS,
  isLowestPriceDeductSupported,
  SERVICE_FEE_REDUCTION,
  USE_ON_OPTIONS,
  USE_ON_TYPES,
} from './constants'

import CouponGenerateType from './CouponGenerateType.vue' // 优惠券生成方式
import CouponSettleMethod from './CouponSettleMethod.vue' // 优惠券结算方式
import CouponValidityPeriod from './CouponValidityPeriod.vue'

const props = defineProps({
  // 页面模式 ：add - 添加  edit - 编辑
  pageMode: {
    type: String,
    default: 'add',
  },
})

const { proxy } = getCurrentInstance()

const couponId = ref(null)

// 优惠券表单数据，包含所有券基础信息
const couponForm = ref({
  name: null, // 券名称
  useOn: USE_ON_TYPES.MOVIE_TICKET, // 适用类型 电影票0，卖品1, 演出2
  couponType: COUPON_TYPES.FULL_REDUCTION, // 券类型 满减0，减至1，通兑2，折扣3，多兑一券4
  // 满减券取值
  fullAmount: {
    fullAmount: 0, // 满减券满多少
    reduceAmount: 0, // 满减券减多少
  },
  // 减至券取值
  reduceAmount: 0, // 减至券减多少
  // 折扣券取值
  discountAmount: 0, // 折扣券折扣多少
  // 通兑券补差规则
  // minAmount 最小金额
  // maxAmount 最大金额
  // supplementAmount 补差金额
  // 例如：0-20元补差0元，21-30元补差5元
  //  [{"minAmount":0,"maxAmount":20,"supplementAmount":0},{"minAmount":21,"maxAmount":30,"supplementAmount":5}]
  exchangeAmount: [],
  // 多兑一券兑换规则
  multiExchangeAmount: [],
  // 服务费减免 0.减免 1.不减免
  reduction: SERVICE_FEE_REDUCTION.NOT_REDUCE, // 服务费减免 0.减免 1.不减免
  userServiceFee: DEFAULT_VALUES.USER_SERVICE_FEE, // 不减免服务费金额 默认3元
  channelScope: CHANNEL_SCOPE.PARTIAL, // 适用渠道类型 全部0，指定1
  channelIds: [], // 适用渠道
  memo: '', // 券说明
  valuable: 0, // 券面值
  remark: '', // 备注
})

// 此处为addCounponBase.vue的props和couponId定义区域，后续可在此添加相关响应式变量或props扩展
function setCouponTypeSpecificValues() {
  console.log('couponForm', couponForm.value)

  switch (couponForm.value.couponType) {
    // 电影票满减券
    case COUPON_TYPES.FULL_REDUCTION:
      couponForm.value.fullAmount = {
        fullAmount: couponForm.value.couponRules[0].priceOrigin / 100,
        reduceAmount: couponForm.value.couponRules[0].priceReduce / 100,
      }
      break
    //   卖品券减至券
    case COUPON_TYPES.REDUCE_TO:
      couponForm.value.reduceAmount
        = couponForm.value.couponRules[0].priceOrigin / 100
      break
    //   通兑券
    case COUPON_TYPES.EXCHANGE:
      couponForm.value.exchangeAmount = couponForm.value.couponRules.map(
        item => ({
          minAmount: item.priceOrigin / 100,
          maxAmount: item.priceReduce / 100,
          supplementAmount: item.priceDiff / 100,
          type: item.matchRule,
        }),
      )
      // console.log("编辑通兑券", couponForm.value.exchangeAmount);
      break
    //   折扣券
    case COUPON_TYPES.DISCOUNT:
      couponForm.value.fullAmount = {
        fullAmount: couponForm.value.couponRules[0].priceOrigin / 100,
        reduceAmount: couponForm.value.couponRules[0].priceReduce / 100,
      }
      // 使用 toFixed 方法限制小数位数为 2
      couponForm.value.discountAmount = (
        couponForm.value.couponRules[0].priceOrigin / 100
      ).toFixed(2)
      break
    //   多对一券
    case COUPON_TYPES.MULTI_EXCHANGE:
      couponForm.value.multiExchangeAmount = couponForm.value.couponRules.map(
        item => ({
          num: item.priceReduce,
          id: item.id,
        }),
      )
      break
  }
}

// 设置渠道
function setChannelValues() {
  if (couponForm.value.channelScope === CHANNEL_SCOPE.PARTIAL) {
    console.log(
      'couponForm.value.couponChannels',
      couponForm.value.couponChannels,
    )

    couponForm.value.channelIds = couponForm.value.couponChannels.map(
      item => item.channelId,
    )
  }
}

// 设置优惠券有效期
function setPeriodValues(couponCanuseConfig) {
  couponValidity.value = {
    validityScope: couponCanuseConfig.validScope, // 有效期方式0.绑定后N天可用 1.指定日期可用
    overdueDay: couponCanuseConfig.overdueDay, // 绑定后N天可用
    validityRange: [couponCanuseConfig.startTime, couponCanuseConfig.endTime], // 指定券可用日期
  }
}

// 设置生成优惠券方式
function setCreateCouponMethodValues(couponCodeCreateConfig) {
  console.log('couponCodeCreateConfig', couponCodeCreateConfig)
  generateType.value = {
    type: couponCodeCreateConfig.generateType, // 生成方式 0.一次生成N张 1.按需生成
    num: couponCodeCreateConfig.num, // 生成数量 当generateType=1时生效
  }
}

// 设置优惠券结算方式
function setSettleMethodValues(couponSettleMethodConfig) {
  console.log('couponSettleMethodConfig', couponSettleMethodConfig)

  settleMethod.value = {
    type: couponSettleMethodConfig.settleMethod, // 结算方式 0.无需结算 1.需要结算
    couponPricedBo: {
      pricedType: couponSettleMethodConfig.couponPriced?.pricedType, // 结算方式 0.固定票价 1.最低票价
      pricedMoney: couponSettleMethodConfig.couponPriced?.pricedMoney / 100, // 结算价格
    },
  }
}

// 该文件为优惠券基础信息设置相关方法
// setChannelValues: 设置券适用渠道
// setPeriodValues: 设置券有效期
// setCreateCouponMethodValues: 设置券生成方式
// setSettleMethodValues: 设置券结算方式
// setCouponMainValues: 外部调用入口，批量设置主信息

function setCouponMainValues(couponMainInfo, couponCanuseConfig, couponCodeCreateConfig, couponSettleMethodConfig) {
  // console.log({
  //   A: couponMainInfo,
  //   BB: couponCanuseConfig,
  //   CCC: couponCodeCreateConfig,
  //   DDD: couponSettleMethodConfig
  // });

  Object.assign(couponForm.value, {
    ...couponForm.value,
    ...couponMainInfo,
    valuable: Number.parseFloat(couponMainInfo.valuable) / 100,
    userServiceFee: Number.parseFloat(couponMainInfo.userServiceFee) / 100,
  })
  // 初始化优惠券类型相关配置
  setCouponTypeSpecificValues()
  // 初始化券适用渠道
  setChannelValues()
  // 初始化优惠券有效期
  setPeriodValues(couponCanuseConfig)
  // 初始化生成优惠券方式
  setCreateCouponMethodValues(couponCodeCreateConfig)
  // 初始化优惠券结算方式
  setSettleMethodValues(couponSettleMethodConfig)
}

defineExpose({
  setCouponMainValues,
})

const channelIdsOptions = ref([]) //  渠道选项

// 券生成方式
const generateType = ref({
  type: 1, // 0.按需生成 1.一次生成N张
  num: 0, // 生成数量
})

// 券结算方式
const settleMethod = ref({
  type: 0,
  couponPricedBo: {
    pricedType: 0,
    pricedMoney: 0,
  },
})

// 券有效时间
const couponValidity = ref({
  validityScope: 0, // 有效期方式0.指定日期可用 1.绑定后N天可用
  overdueDay: 1, // 绑定后N天可用
  validityRange: [], // 指定券可用日期
})

// 计算属性：判断是否为订单券
/**
 * 判断是否为订单券
 * @returns {boolean} 是否为订单券
 */
const isOrderCoupon = computed(() => {
  const couponTypeOption = COUPON_TYPE_OPTIONS.find(item => item.value === couponForm.value.couponType)
  return couponTypeOption?.des.text === COUPON_TYPE_SCOPE.ORDER
})

/**
 * 构建优惠券规则数据
 * @returns {Array} 优惠券规则数组
 */
function buildCouponRules() {
  switch (couponForm.value.couponType) {
    case COUPON_TYPES.FULL_REDUCTION: // 满减券
      return [
        {
          priceOrigin: couponForm.value.fullAmount.fullAmount * 100,
          priceReduce: couponForm.value.fullAmount.reduceAmount * 100,
        },
      ]
    case COUPON_TYPES.REDUCE_TO: // 减至券
      return [
        {
          priceOrigin: Number.parseFloat(couponForm.value.reduceAmount * 100).toFixed(0),
        },
      ]
    case COUPON_TYPES.EXCHANGE: // 通兑券
      return couponForm.value.exchangeAmount.map(item => ({
        priceOrigin: item.minAmount * 100,
        priceReduce: item.maxAmount * 100,
        priceDiff: item.supplementAmount * 100,
        matchRule: item.type,
      }))
    case COUPON_TYPES.DISCOUNT: // 折扣券
      return [
        {
          priceOrigin: Number.parseFloat(couponForm.value.discountAmount * 100).toFixed(0),
        },
      ]
    case COUPON_TYPES.MULTI_EXCHANGE: // 多对一券
      return couponForm.value.multiExchangeAmount.map(item => ({
        priceOrigin: 1,
        priceReduce: item.num,
      }))
    default:
      return []
  }
}

/**
 * 构建渠道信息
 * @returns {Array} 渠道信息数组
 */
function buildCouponChannels() {
  return couponForm.value.channelIds.map(id => ({
    channelId: id,
    channelName: channelIdsOptions.value.find(item => item.value === id)?.label || '',
  }))
}

/**
 * 构建有效期配置
 * @returns {object} 有效期配置对象
 */
function buildValidityConfig() {
  const { validityScope, validityRange, overdueDay } = couponValidity.value

  return {
    validScope: validityScope,
    overdueDay,
    startTime: validityScope === 0 && validityRange.length > 0
      ? `${validityRange[0]} 00:00:00`
      : null,
    endTime: validityScope === 0 && validityRange.length > 0
      ? `${validityRange[1]} 23:59:59`
      : null,
  }
}

/**
 * 构建表单提交数据
 * @returns {object} 完整的表单数据对象
 */
function buildFormData() {
  return {
    couponMainInfo: {
      name: couponForm.value.name,
      useOn: couponForm.value.useOn,
      couponType: couponForm.value.couponType,
      reduction: couponForm.value.reduction,
      userServiceFee: couponForm.value.userServiceFee * 100,
      channelScope: couponForm.value.channelScope,
      id: props.pageMode === 'add' ? null : couponForm.value.id,
      couponChannels: buildCouponChannels(),
      memo: couponForm.value.memo,
      valuable: Math.round(couponForm.value.valuable) * 100,
      remark: couponForm.value.remark,
      couponRules: buildCouponRules(),
    },
    couponCodeCreateConfig: {
      generateType: generateType.value.type,
      num: generateType.value.num,
    },
    couponSettleMethodConfig: {
      settleMethod: settleMethod.value.type,
      couponPriced: {
        pricedType: settleMethod.value.couponPricedBo.pricedType,
        pricedMoney: settleMethod.value.couponPricedBo.pricedMoney * 100,
      },
    },
    couponCanuseConfig: buildValidityConfig(),
  }
}

// 以上为券表单相关的响应式数据定义
// generateType：券生成方式相关数据
// settleMethod：券结算方式相关数据
// couponValidity：券有效期相关数据

const couponFormRules = {
  name: [{ required: true, message: '请填写券名称', trigger: 'blur' }],
  channelScope: [
    { required: true, message: '请选择适用渠道', trigger: 'change' },
  ],
  channelIds: [
    {
      required: true,
      message: '请选择适用渠道',
      trigger: 'change',
      type: 'array',
    },
  ],
  reduction: [
    { required: true, message: '请选择是否减免服务费', trigger: 'change' },
  ],
  fullAmount: [
    { required: true, message: '请填写满减券满多少', trigger: 'blur' },
  ],
  reduceAmount: [
    {
      required: true,
      trigger: 'change',
      message: '请填写减至券减多少',
      validator: (rule, value, callback) => {
        if (value === 0) {
          callback(new Error('减至券减多少不能为0'))
        }
        else {
          callback()
        }
      },
    },
  ],
  multiExchangeAmount: [
    {
      required: true,
      message: '请至少添加一个多对一兑换规则',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (value.length === 0) {
          callback(new Error('请至少添加一个多对一兑换规则'))
        }
        else {
          callback()
        }
      },
    },
  ],
  exchangeAmount: [
    {
      required: true,
      message: '请至少添加一个兑换规则',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (value.length === 0) {
          callback(new Error('请至少添加一个兑换规则'))
        }
        else {
          callback()
        }
      },
    },
  ],
  validityRange: [
    {
      required: true,
      message: '请选择开始结束时间',
      trigger: 'change',
      validator: (rule, value, callback) => {
        // callback(new Error('222222222请选择开始结束时间'))
        if (
          couponValidity.value.validityRange
          && couponValidity.value.validityRange.length < 1
        ) {
          // console.log({'couponValidity.value.validityRange':couponValidity.value.validityRange});
          callback(new Error('请选择开始结束时间'))
        }
        else {
          callback()
        }
      },
    },
  ],
}

// 添加兑换规则
function addExchangeAmount() {
  couponForm.value.exchangeAmount.push({
    minAmount: 0,
    maxAmount: 0,
    supplementAmount: 0,
    // 补差类型
    type: 0, // 0为优惠金额补差1.为按最低票价抵扣
  })
}
// 删除规则
function deleteExchangeAmount(row, index) {
  couponForm.value.exchangeAmount.splice(index, 1)
}

// 添加一条多对一券兑换规则
function addMultiExchangeAmount() {
  // 默认有3条规则
  if (couponForm.value.multiExchangeAmount.length == 0) {
    for (let i = 0; i < 3; i++) {
      couponForm.value.multiExchangeAmount.push({
        num: i + 1,
      })
    }
  }
  else {
    couponForm.value.multiExchangeAmount.push({
      num: couponForm.value.multiExchangeAmount.length + 1,
    })
  }
}

// 删除规则
function deleteMultiExchangeAmount(row, index) {
  proxy.$modal
    .confirm('是否删除该兑换规则?')
    .then(() => {
      // 确定操作
      couponForm.value.multiExchangeAmount.splice(index, 1)
    })
    .catch(() => {
      // 取消操作
    })
}

// 加载渠道选项
function loadOptions() {
  // 调用获取所有渠道的接口，参数为只获取启用状态的渠道
  getAllChannel({ channelStatus: 1 }).then((row) => {
    // 显示加载中提示
    proxy?.$modal.loading('正在加载渠道')
    const { data } = row
    // 将接口返回的数据映射为下拉选项
    channelIdsOptions.value = data.map((row) => {
      // 关闭加载中提示
      proxy?.$modal.closeLoading()
      return {
        label: row.channelName, // 渠道名称作为label
        value: row.id, // 渠道id作为value
      }
    })
  })
}

// 监听适用类型变化，处理通兑券补差类型限制
watch(
  () => couponForm.value.useOn,
  (newUseOn) => {
    // 如果当前是通兑券且选择了按最低票价抵扣，但新的适用类型不支持，则重置为优惠金额补差
    if (
      couponForm.value.couponType === COUPON_TYPES.EXCHANGE
      && couponForm.value.exchangeAmount.length > 0
      && couponForm.value.exchangeAmount[0].type
      === EXCHANGE_DIFF_TYPE.LOWEST_PRICE_DEDUCT
      && !isLowestPriceDeductSupported(newUseOn)
    ) {
      couponForm.value.exchangeAmount[0].type = EXCHANGE_DIFF_TYPE.AMOUNT_DIFF
    }
  },
)

// 监听券类型变化
watch(
  () => couponForm.value.couponType,
  (newCouponType) => {
    const couponTypeOption = COUPON_TYPE_OPTIONS.find(item => item.value === newCouponType)
    // 订单券固定不免服务费
    if (couponTypeOption.des.text === COUPON_TYPE_SCOPE.ORDER) {
      couponForm.value.reduction = SERVICE_FEE_REDUCTION.NOT_REDUCE
    }
    // 单品券保持用户当前选择，不做强制修改
  },
)

onMounted(() => {
  loadOptions()
})

function submitForm() {
  // 确定操作
  proxy.$refs.couponFormRef
    .validate((valid) => {
      if (valid) {
        const formData = buildFormData()
        console.info(formData)
        proxy.$modal
          .confirm('确认提交？')
          .then(() => {
            // this.$emit("submitForm", formData);
            proxy.$modal.loading('正在保存数据,请稍后...')
            if (props.pageMode === 'add') {
              createCoupon(formData).then((res) => {
                const { data, code, message } = res
                proxy.$modal.closeLoading()
                if (code === 200) {
                  // proxy.$modal.msgSuccess( "新增成功");
                  proxy.$modal
                    .confirm('新增成功,是否继续设置券使用条件?', '提示')
                    .then(() => {
                      couponId.value = data.couponMainInfo.id
                      proxy.$emit('nextStep', couponId.value)
                    })
                    .catch(() => {
                      proxy.$emit('refresh')
                      proxy.$emit('close')
                    })
                  // emit("refresh");
                  // emit("close");
                }
                else {
                  proxy.$modal.msgError(message)
                }
              })
            }
            else {
              changeCouponStatus(formData).then((res) => {
                const { data, code, message } = res
                proxy.$modal.closeLoading()
                if (code === 200) {
                  // proxy.$modal.msgSuccess( "新增成功");
                  proxy.$modal
                    .confirm('修改成功,是否继续设置券使用条件?', '提示')
                    .then(() => {
                      couponId.value = data.couponMainInfo.id
                      proxy.$emit('nextStep', couponId.value)
                    })
                    .catch(() => {
                      proxy.$emit('refresh')
                      proxy.$emit('close')
                    })
                  // emit("refresh");
                  // emit("close");
                }
                else {
                  proxy.$modal.msgError(message)
                }
              })
            }

            // proxy.$emit("submitForm", formData);
            proxy.$modal.closeLoading()
          })
          .catch(() => {
            // proxy.$modal.closeLoading();
          })
      }
      else {
        console.log('error submit!!')
        return false
      }
    })
    .catch(() => {
      // 取消操作
    })
}

function cancel() {
  console.log('取消操作')
  proxy.$emit('close')
}

function validateReduceAmount(rule, value, callback) {
  console.log(value, couponForm.value.fullAmount.fullAmount)

  if (value > couponForm.value.fullAmount.fullAmount) {
    callback(new Error('减免金额必须小于等于满减金额'))
  }
  else {
    callback()
  }
}
</script>

<template>
  <div>
    <el-divider content-position="left">
      <span>基本信息</span>
    </el-divider>
    <el-form
      ref="couponFormRef"
      :model="couponForm"
      :disabled="props.pageMode === 'view'"
      :rules="couponFormRules"
      label-width="150px"
    >
      <!-- 券名称 -->
      <el-form-item label="券名称" prop="name" aria-label="券名称">
        <el-input
          v-model="couponForm.name"
          placeholder="请输入券名称"
          size="large"
          :maxlength="INPUT_LIMITS.NAME_MAX_LENGTH"
          show-word-limit
        />
      </el-form-item>
      <!-- 适用类型 Start -->
      <el-form-item label="适用商品" required>
        <el-radio-group
          v-model="couponForm.useOn"
          :disabled="pageMode === 'edit'"
          @change="couponForm.couponType = COUPON_TYPES.FULL_REDUCTION"
        >
          <el-radio
            v-for="item in USE_ON_OPTIONS"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 适用类型 End -->
      <!-- 券类型 -->
      <el-form-item required>
        <template #label>
          <div style="display: flex; align-items: center;">
            <span>券类型</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="一笔订单最多可使用一种类型的券"
              placement="top"
            >
              <FaIcon name="question" />
            </el-tooltip>
          </div>
        </template>

        <template #default>
          <el-radio-group
            v-model="couponForm.couponType"
            :disabled="pageMode === 'edit'"
          >
            <el-radio
              v-for="item in COUPON_TYPE_OPTIONS"
              :key="item.value"
              :value="item.value"
              :disabled="
                item.value === COUPON_TYPES.MULTI_EXCHANGE
                  && couponForm.useOn !== USE_ON_TYPES.MOVIE_TICKET
              "
            >
              {{ item.label }}
              <el-tag
                effect="plain"
                style="margin-left: 10px;"
                :type="item.des.type"
                size="small"
              >
                {{ item.des.text }}
              </el-tag>
            </el-radio>
          </el-radio-group>
        </template>
      </el-form-item>
      <!-- 券类型：满减券规则 -->
      <el-form-item
        v-if="couponForm.couponType === COUPON_TYPES.FULL_REDUCTION"
        label="优惠规则"
        required
        prop="fullAmount.reduceAmount"
        :rules="[{ validator: validateReduceAmount, trigger: 'blur' }]"
      >
        <el-text>整笔订单满</el-text>
        <el-input-number
          v-model="couponForm.fullAmount.fullAmount"
          :disabled="pageMode === 'edit'"
          :min="0"
          :max="INPUT_LIMITS.AMOUNT_MAX"
          :step="0.01"
          controls-position="right"
          placeholder="请输入满减券满多少"
          style="margin: 0 10px;"
        />
        <span>减</span>
        <el-input-number
          v-model="couponForm.fullAmount.reduceAmount"
          :disabled="pageMode === 'edit'"
          :min="0"
          :max="INPUT_LIMITS.AMOUNT_MAX"
          :step="0.01"
          controls-position="right"
          placeholder="请输入满减券减多少"
          style="margin: 0 10px;"
        />
        <span>，一笔订单最多可使用一张券</span>
      </el-form-item>
      <!-- 券类型：减至券规则 -->
      <el-form-item
        v-if="couponForm.couponType === COUPON_TYPES.REDUCE_TO"
        label="优惠规则"
        required
        prop="reduceAmount"
      >
        <el-text :style="{ margin: '0 10px' }">
          {{
            USE_ON_OPTIONS.find((item) => item.value === couponForm.useOn)
              ?.label
          }}减至
        </el-text>
        <el-input-number
          v-model="couponForm.reduceAmount"
          :disabled="pageMode === 'edit'"
          :min="0"
          :max="INPUT_LIMITS.REDUCE_TO_MAX"
          :step="0.01"
          controls-position="right"
          placeholder="请输入减至券减多少"
        />
        <span :style="{ marginLeft: '10px' }">
          元，{{
            USE_ON_OPTIONS.find((item) => item.value === couponForm.useOn)
              ?.label
          }}最多可使用一张券
        </span>
      </el-form-item>

      <!-- 券类型：通兑券规则 Start -->
      <el-form-item
        v-if="couponForm.couponType === COUPON_TYPES.EXCHANGE"
        label="优惠规则"
        required
        prop="exchangeAmount"
      >
        <div v-if="couponForm.exchangeAmount.length" style="width: 100%;">
          <el-radio-group
            v-model="couponForm.exchangeAmount[0].type"
            :disabled="pageMode === 'edit'"
            style="margin-bottom: 10px;"
          >
            <el-radio :label="EXCHANGE_DIFF_TYPE.AMOUNT_DIFF">
              优惠金额补差
            </el-radio>
            <el-radio
              v-if="isLowestPriceDeductSupported(couponForm.useOn)"
              :label="EXCHANGE_DIFF_TYPE.LOWEST_PRICE_DEDUCT"
            >
              按最低票价抵扣
            </el-radio>
          </el-radio-group>
          <br>
        </div>
        <!-- 优惠金额补差 Start -->
        <el-table
          v-if="couponForm.exchangeAmount?.[0]?.type === EXCHANGE_DIFF_TYPE.AMOUNT_DIFF"
          :data="couponForm.exchangeAmount"
          :show-header="false"
          :cell-style="{ border: `${0}px` }"
        >
          <el-table-column>
            <template #default="{ row, $index }">
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-input-number
                    v-model="row.minAmount"
                    style="width: 100%;"
                    :disabled="pageMode === 'edit'"
                    :min="0"
                    :max="INPUT_LIMITS.AMOUNT_MAX"
                    :step="0.01"
                    controls-position="right"
                    placeholder="请输入最小金额"
                  >
                    <template #prefix>
                      {{
                        USE_ON_OPTIONS.find(
                          (item) => item.value === couponForm.useOn,
                        )?.label
                      }}
                      &gt;
                    </template>
                    <template #suffix>
                      元
                    </template>
                  </el-input-number>
                </el-col>
                <el-col :span="7">
                  <el-input-number
                    v-model="row.maxAmount"
                    style="width: 100%;"
                    :disabled="pageMode === 'edit'"
                    :min="0"
                    :max="INPUT_LIMITS.AMOUNT_MAX"
                    :step="0.01"
                    controls-position="right"
                    placeholder="请输入最大金额"
                  >
                    <template #prefix>
                      {{
                        USE_ON_OPTIONS.find(
                          (item) => item.value === couponForm.useOn,
                        )?.label
                      }}
                      &lt;=
                    </template>
                    <template #suffix>
                      元
                    </template>
                  </el-input-number>
                </el-col>
                <el-col :span="6">
                  <el-input-number
                    v-model="row.supplementAmount"
                    style="width: 100%;"
                    :disabled="pageMode === 'edit'"
                    :min="0"
                    :max="INPUT_LIMITS.AMOUNT_MAX"
                    :step="0.01"
                    controls-position="right"
                    placeholder="请输入补差金额"
                  >
                    <template #prefix>
                      补差：
                    </template>
                    <template #suffix>
                      元
                    </template>
                  </el-input-number>
                </el-col>
                <el-col
                  :span="3"
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
"
                >
                  <el-button
                    type="primary"
                    class="mx-4"
                    :disabled="pageMode === 'edit'"
                    link
                    @click="addExchangeAmount"
                  >
                    添加规则
                  </el-button>
                  <el-button
                    type="danger"
                    :disabled="pageMode === 'edit'"
                    link
                    @click="deleteExchangeAmount(row, $index)"
                  >
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <!-- 优惠金额补差 End -->
        <!-- 按最低票价抵扣 Start -->
        <div v-else>
          <!-- 按最低票价抵扣,添加一个最大抵扣金额 -->
          <el-input-number
            v-if="couponForm.exchangeAmount[0]"
            v-model="couponForm.exchangeAmount[0].supplementAmount"
            style="width: 100%;"
            :disabled="pageMode === 'edit'"
            :min="INPUT_LIMITS.MIN_AMOUNT"
            :max="INPUT_LIMITS.AMOUNT_MAX"
            :step="1"
            controls-position="right"
            placeholder="请输入最大抵扣金额"
          >
            <template #prefix>
              最大抵扣金额：
            </template>
            <template #suffix>
              元
            </template>
          </el-input-number>
          <el-button
            v-else
            type="primary"
            link
            icon="plus"
            @click="addExchangeAmount"
          >
            添加规则
          </el-button>
        </div>
        <!-- 按最低票价抵扣 End -->
      </el-form-item>
      <!-- End -->
      <!-- 券类型：折扣券规则 -->
      <el-form-item
        v-if="couponForm.couponType === COUPON_TYPES.DISCOUNT"
        label="优惠规则"
      >
        <el-text style="margin: 0 10px;">
          整笔订单打
        </el-text>
        <el-input-number
          v-model="couponForm.discountAmount"
          :min="INPUT_LIMITS.MIN_AMOUNT"
          :max="INPUT_LIMITS.DISCOUNT_MAX"
          :step="0.01"
          controls-position="right"
          placeholder="请输入折扣券折扣多少"
          :disabled="pageMode === 'edit'"
        />
        <span style="margin-left: 10px;">折，一笔订单最多可使用一张券</span>
      </el-form-item>
      <!-- 券类型：多兑一换券规则 -->
      <el-form-item
        v-if="couponForm.couponType === COUPON_TYPES.MULTI_EXCHANGE"
        prop="multiExchangeAmount"
        label="优惠规则"
        required
      >
        <el-button
          v-if="!couponForm.multiExchangeAmount.length"
          :disabled="pageMode === 'edit'"
          type="primary"
          link
          icon="plus"
          @click="addMultiExchangeAmount"
        >
          添加规则
        </el-button>
        <el-row
          v-for="(item, index) in couponForm.multiExchangeAmount"
          :key="index"
          style="width: 100%; margin-bottom: 10px;"
          :gutter="10"
        >
          <el-col :span="8">
            <el-input-number
              v-model="item.num"
              :min="1"
              :max="INPUT_LIMITS.MULTI_EXCHANGE_MAX"
              :step="1"
              controls-position="right"
              placeholder="请输入兑换规则"
              :disabled="pageMode === 'edit'"
              style="width: 100%;"
            >
              <template #prefix>
                规则 {{ index + 1 }} :
              </template>
              <template #suffix>
                兑1张
              </template>
            </el-input-number>
          </el-col>
          <el-col :span="3">
            <el-button
              :disabled="pageMode === 'edit'"
              type="primary"
              link
              icon="plus"
              @click="addMultiExchangeAmount"
            >
              添加规则
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button
              :disabled="pageMode === 'edit'"
              type="danger"
              link
              icon="delete"
              @click="deleteMultiExchangeAmount(item, index)"
            >
              删除
            </el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <!-- 服务费 -->
      <el-form-item
        v-if="couponForm.useOn === USE_ON_TYPES.MOVIE_TICKET"
        label="服务费减免"
        required
      >
        <el-radio-group
          v-model="couponForm.reduction"
          :disabled="pageMode === 'edit' || isOrderCoupon"
        >
          <el-radio :value="SERVICE_FEE_REDUCTION.REDUCE">
            减免
            <el-tag v-if="isOrderCoupon" type="warning" size="small" style="margin-left: 8px;">
              订单券不可选
            </el-tag>
          </el-radio>
          <el-radio :value="SERVICE_FEE_REDUCTION.NOT_REDUCE">
            不减免
            <el-tag v-if="isOrderCoupon" type="info" size="small" style="margin-left: 8px;">
              订单券固定不免
            </el-tag>
          </el-radio>
        </el-radio-group>
        <div v-if="isOrderCoupon" style="margin-top: 8px; font-size: 12px; color: #909399;">
          <FaIcon name="info" />
          订单券（满减券、折扣券）固定不免服务费，不可修改
        </div>
      </el-form-item>
      <!-- userServiceFee -->
      <!-- {{ couponForm.reduction === SERVICE_FEE_REDUCTION.NOT_REDUCE }}
       {{  isOrderCoupon }}
       {{ couponForm.useOn === USE_ON_TYPES.MOVIE_TICKET }} -->
      <el-form-item
        v-if="(couponForm.reduction === SERVICE_FEE_REDUCTION.NOT_REDUCE || isOrderCoupon) && couponForm.useOn === USE_ON_TYPES.MOVIE_TICKET"
        label="服务费收取金额"
        required
      >
        <el-input-number
          v-model="couponForm.userServiceFee"
          :min="0"
          :max="INPUT_LIMITS.SERVICE_FEE_MAX"
          :step="0.01"
          :disabled="pageMode === 'edit'"
        />
        <div v-if="isOrderCoupon" style="margin-top: 8px; font-size: 12px; color: #909399;">
          <FaIcon name="info" />
          订单券固定不免服务费，可设置服务费收取金额
        </div>
      </el-form-item>

      <!-- 适用渠道类型 -->
      <el-form-item label="适用渠道类型" required>
        <el-radio-group v-model="couponForm.channelScope">
          <!-- 修改后 -->
          <el-radio :value="CHANNEL_SCOPE.ALL" disabled>
            全部
          </el-radio>
          <el-radio :value="CHANNEL_SCOPE.PARTIAL">
            部分
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="couponForm.channelScope === CHANNEL_SCOPE.PARTIAL"
        label="适用渠道"
        required
        prop="channelIds"
      >
        <el-select
          v-model="couponForm.channelIds"
          :max-collapse-tags="8"
          collapse-tags
          collapse-tags-tooltip
          multiple
          autocomplete
          filterable
          clearable
          placeholder="请选择适用渠道"
        >
          <el-option
            v-for="item in channelIdsOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 其他说明 -->
      <el-form-item label="其他说明">
        <el-input
          v-model="couponForm.memo"
          type="textarea"
          show-word-limit
          clearable
          :maxlength="INPUT_LIMITS.MEMO_MAX_LENGTH"
          :rows="3"
          placeholder="请输入其他说明"
        />
      </el-form-item>
      <!-- 优惠券价值 -->
      <el-form-item label="优惠券价值">
        <el-input-number
          v-model="couponForm.valuable"
          :min="0"
          :precision="2"
          :step="0.5"
          :max="INPUT_LIMITS.VALUABLE_MAX"
        >
          <template #suffix>
            元
          </template>
        </el-input-number>
      </el-form-item>
      <!-- 备注 -->
      <el-form-item label="备注">
        <el-input
          v-model="couponForm.remark"
          type="textarea"
          show-word-limit
          clearable
          :maxlength="INPUT_LIMITS.REMARK_MAX_LENGTH"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
      <!-- 有效期 -->
      <CouponValidityPeriod v-model="couponValidity" />
      <!--        生成方式 -->
      <CouponGenerateType v-model="generateType" />
      <!-- {{ settleMethod }} -->
      <!--        券计算方式 -->
      <CouponSettleMethod
        v-if="couponForm.useOn === 0"
        v-model="settleMethod"
      />
    </el-form>
    <div class="dialog-footer">
      <el-button type="primary" @click="submitForm">
        {{
          pageMode === "add" ? "新增" : "保存"
        }}
      </el-button>
      <el-button @click="cancel">
        取 消
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dialog {
  &-footer {
    text-align: center;
  }
}
</style>
