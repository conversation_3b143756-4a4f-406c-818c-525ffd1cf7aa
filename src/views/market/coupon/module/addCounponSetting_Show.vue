<script setup>
import { defineEmits, defineProps } from 'vue'
import selectShowDialog from '@/components/selectShowDialog'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      showScope: 0,
      showScheduleIds: [],
      showPopup: {
        show: false,
        showIds: [],
        cinemaIds: [],

      },
      showScheduleList: [],
    }),
  },
})

// Emit
const emit = defineEmits(['update:modelValue'])

function updateScheduleIds() {
  // Implement the logic to update schedule IDs
}

function handleRemoveShowGroup(index) {
  const newShowScheduleList = [...props.modelValue.showScheduleList]

  newShowScheduleList.splice(index, 1)
  props.modelValue.showScheduleIds = newShowScheduleList.map(item => item.selectScheduleIds).flat()
  // emit('update:showScheduleList', newShowScheduleList);
  emit('update:modelValue', {
    ...props.modelValue,
    showScheduleList: newShowScheduleList,
  })
}

function handleShowPopup() {
  console.log('handleShowPopup', props.modelValue)
  props.modelValue.showPopup.showIds = props.modelValue.showScheduleList.map(item => item.showId)
  props.modelValue.showPopup.cinemaIds = []
  if (props.modelValue.cinemaScope > 0) {
    props.modelValue.showPopup.cinemaIds = props.modelValue.cinemaIds.map(item => item.id)
  }
  props.modelValue.showPopup.show = true
}

function handleShowSelect(data) {
  props.modelValue.showScheduleIds = data.scheduleIds
  props.modelValue.showScheduleList = data.slectShowList
}

watch(
  () => props.modelValue,
  (newVal) => {
    // console.log('监听modelValue', newVal);

    emit('update:modelValue', newVal)
  },
  { deep: true },
)
</script>

<template v-if="modelValue.showScope !== undefined">
  <!-- <pre v-is-dev>
    {{ modelValue }}
  </pre> -->
  <el-form-item label="适用演出">
    <el-radio-group v-model="modelValue.showScope">
      <el-radio :value="0">
        不限
      </el-radio>
      <el-radio :value="1">
        指定的演出
      </el-radio>
      <el-radio :value="2">
        排除的演出
      </el-radio>
    </el-radio-group>
  </el-form-item>
  <el-form-item v-if="modelValue.showScope" :label="`${modelValue.showScope === 1 ? '指定' : '排除'}场次`">
    <div>
      <el-button type="primary" @click="handleShowPopup">
        选择演出
      </el-button>
      <el-text type="info" style="margin-left: 16px;">
        已{{ (modelValue.showScope === 1 ? '指定' : '排除') + modelValue.showScheduleIds.length }}个场次
      </el-text>
      <div v-for="(show, index) in modelValue.showScheduleList" :key="show.id" class="selectshow-group">
        <div class="item-label">
          <div class="text-overhide">
            {{ show.showName }}
          </div>
          <div class="text-overhide">
            {{ show.cinemaName }}
          </div>
        </div>
        <el-select
          v-model="show.selectScheduleIds"
          multiple
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="3"
          placeholder="选择场次"
          style="width: 500px;margin: 0 24px;"
          @change="updateScheduleIds"
        >
          <el-option
            v-for="item in show.showSchedules"
            :key="item.scheduleId"
            :label="item.timeText"
            :value="item.scheduleId"
          />
        </el-select>
        <el-icon size="24" color="#D9001B" style="cursor: pointer;" @click="handleRemoveShowGroup(index)">
          <Remove />
        </el-icon>
      </div>
      <selectShowDialog
        v-if="modelValue.showPopup.show"
        :model-value="modelValue.showPopup.show"
        :select-ids="modelValue.showPopup.showIds"
        :cinema-ids="modelValue.showPopup.cinemaIds"
        :cinema-scope="modelValue.cinemaScope"
        @commit="handleShowSelect"
        @close="modelValue.showPopup.show === false"
      />
    </div>
  </el-form-item>
</template>

<style scoped>
.selectshow-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.item-label {
  display: flex;
  flex-direction: column;
  width: 200px;
}

.text-overhide {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
