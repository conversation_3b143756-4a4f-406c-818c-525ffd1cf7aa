<script setup name="addCounponSettingTicket">
import { getCurrentInstance, nextTick, onMounted, reactive, ref, watch } from 'vue'
import cinemaSelector from '@/components/cinemaSelector/index.vue'
import FilmSelector from '@/components/FilmSelector/index.vue'
import filmVersion from '@/components/filmVersion/index.vue'
// import selectShowDialog from '@/components/selectShowDialog/index.vue'
import WeekTime from '@/components/WeekTime/index.vue'
// import HallSelectorBase from '@/views/market/coupon/module/hallSelector-base.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      cinemaScope: 0, // 限制影院方式：0 不限 1 指定影院 2 排除选择的影院
      cinemaIds: [],
      filmIds: [],
      filmScope: 0, // 适用影片类型 0.不限 1.指定影片 2.指定最低限价影片
      lowestPrice: 0,
      filmVersions: [], // 适用影片版本
      periodAddBos: [], // 适用时段：全部时段0，指定时段1
    }),
  },
  pageMode: {
    type: String,
    default: 'add',
  },
})

// 向外暴漏一个方法供父组件调用获取couponForm
const emit = defineEmits(['update:modelValue'])

const { proxy } = getCurrentInstance()

const couponForm = ref({
  ...props.modelValue,
})
// console.log("props.modelValue", props.modelValue)
onMounted(() => {
  couponForm.value = props.modelValue
})

watch(
  couponForm,
  (newVal) => {
    emit('update:modelValue', newVal)
  },
  { deep: true },
)

// 完成影城选择
function cinemaComplete(cinemas, customIndex) {
  console.log('完成', cinemas, customIndex)
  let newCinemaList = []
  cinemas.map((cinema) => {
  //   如果该影院不存在，则添加到数组中
    console.log(cinema, 'cinema')
    const index = couponForm.value.cinemaIds.findIndex(item => item.cinemaId === cinema.id)
    console.log({ index })

    //
    if (index === -1) {
      newCinemaList.push({
        cinemaId: cinema.id,
        name: cinema.name,
        selectValue: [],
      })
    }
    else {
      // 如果该影院存在，则删除该影院
      // couponForm.cinemaIds.splice(index, 1);
      newCinemaList.push(couponForm.value.cinemaIds[index])
    }
  })
  couponForm.value.cinemaIds = []
  setTimeout(() => {
    couponForm.value.cinemaIds = newCinemaList
  }, 100)
}

// 完成影片选择
function filmComplete(films) {
  // log
}

// 添加影片版本
function addFilmVersion(filmVersion) {
  console.log()
  filmVersion.forEach((item) => {
    couponForm.value.filmVersions.push(item)
  })
}

// 移除影院
function removeCinema(cinema) {
  // const index = couponForm.value.cinemaIds.indexOf(cinema);
  proxy.$confirm('确认删除该影院吗？').then(() => {
    const newCinemaIds = couponForm.value.cinemaIds.filter(item => item.cinemaId !== cinema.cinemaId)
    couponForm.value.cinemaIds = []
    setTimeout(() => {
      couponForm.value.cinemaIds = newCinemaIds
    }, 100)
  })
}

// 删除影片版本
function removeFilmVersion(filmVersion) {
  // console.log(couponForm.filmVersions)
  filmVersion.forEach((item) => {
    const index = couponForm.value.filmVersions.indexOf(item)
    if (index !== -1) {
      couponForm.value.filmVersions.splice(index, 1)
    }
  })
}
</script>

<template>
  <div>
    <!--    {{ couponForm }} -->
    <el-form :model="couponForm" :disabled="pageMode === 'view'" label-width="150px">
      <el-form-item label="限制影院方式">
        <el-radio-group v-model="couponForm.cinemaScope">
          <el-radio :value="0">
            不限
          </el-radio>
          <el-radio :value="1">
            指定影院
          </el-radio>
          <el-radio :value="2">
            排除选择的影院
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="couponForm.cinemaScope > 0" label="限制影院">
        <!-- {{ couponForm.cinemaIds }} -->
        <cinema-selector :model-value="couponForm.cinemaIds" @on-complete="cinemaComplete" />
        <el-text v-if="couponForm.cinemaScope === 1" style="margin-left: 1vw;">
          已选择{{ couponForm.cinemaIds?.length }}家影院
        </el-text>
        <el-text v-if="couponForm.cinemaScope === 2" style="margin-left: 1vw;">
          已排除{{ couponForm.cinemaIds?.length }}家影院
        </el-text>
        <div v-if="couponForm.cinemaScope && couponForm.cinemaIds" style="width: 100%; margin-top: 10px;">
          <el-tag
            v-for="item in couponForm.cinemaIds" :key="item" closable :type="couponForm.cinemaScope === 2 ? 'danger' : 'primary'"
            style="margin-right: 8px;"
            @close="removeCinema(item)"
          >
            {{ item.name }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item v-if="couponForm.cinemaScope > 0 && couponForm.cinemaIds?.length > 0" label="影厅范围">
        <template #label>
          <div style="display: flex; align-items: center;">
            <span>影厅范围</span>
            <el-tooltip
              class="item" effect="dark" :content="`选中的影厅${couponForm.cinemaScope === 2 ? '不' : ''
              }可使用此优惠券`" placement="top"
            >
              <el-icon>
                <QuestionFilled color="#999" />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <template v-if="couponForm.cinemaIds.length" #default="{ row }">
          <!-- {{ couponForm.cinemaIds }} -->
          <div
            v-for="(cinema, index) in couponForm.cinemaIds" :key="index"
            style="width: 99%; margin-bottom: 10px; margin-left: 10px;"
          >
            <el-row :gutter="20" class="cinemaHall">
              <el-col :span="4" style="padding: 1rem; background-color: rgb(0 0 0 / 4%);">
                <div class="cinemaHall-title">
                  <el-text>{{ cinema.name }}</el-text>
                </div>
              </el-col>
              <el-col :span="20" style="padding: 0 1rem;">
                <!--                  全选、部分影院 -->
                {{ cinema }}
                <!-- <HallSelectorBase v-model="cinema.selectValue" :cinema-id="cinema.cinemaId" /> -->
              </el-col>
            </el-row>
          </div>
        </template>
      </el-form-item>
      <!--        电影券限制条件Star -->
      <el-form-item label="适用时段">
        <WeekTime v-model="couponForm.periodAddBos" />
      </el-form-item>
      <el-form-item label="适用影片类型">
        <el-radio-group v-model="couponForm.filmScope">
          <el-radio :value="0">
            不限
          </el-radio>
          <el-radio :value="1">
            指定影片
          </el-radio>
          <el-radio :value="2">
            指定最低限价影片
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="couponForm.filmScope == 1" label="指定影片">
        <div>
          <!--           {{ couponForm.filmIds }} -->
          <!--          <film-selector v-model="couponForm.filmIds" @onComplete="filmComplete" /> -->
          <FilmSelector v-model="couponForm.filmIds" />
          <div style="margin-top: 4px;">
            已选择{{ couponForm.filmIds?.length }}部影片
          </div>
        </div>
      </el-form-item>
      <el-form-item v-if="couponForm.filmScope == 2" label="适用最低限价">
        <el-input-number
          v-model="couponForm.lowestPrice" :min="0" :max="99999999" :step="0.01"
          controls-position="right" placeholder="请输入最低限价金额"
        />
        <span style="margin-left: 10px;">元</span>
      </el-form-item>
      <el-form-item label="适用影片版本">
        <film-version :default-checked="couponForm.filmVersions" @add="addFilmVersion" @del="removeFilmVersion" />
      </el-form-item>
      <!--        电影券限制条件End -->
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.cinemaHall {

  // border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(245 245 245 / 99%);
  }
}
</style>
