<script setup>
import { defineEmits, defineProps, watch } from 'vue'

// Props
const props = defineProps({
  // settleMethod: {
  //   type: Object,
  //   default: {
  //     type: 0,
  //     couponPricedBo: {
  //       pricedType: 0,
  //       pricedMoney: 0
  //     }
  //   }
  // },
  // couponPricedBo: Object
  modelValue: {
    type: Object,
    default: {
      type: 0,
      couponPricedBo: {
        pricedType: 0,
        pricedMoney: 0,
      },
    },
  },
})

// Emit
const emit = defineEmits(['update:modelValue'])

// Watch for changes and emit updates
watch(() => props.modelValue, newVal => emit('update:modelValue', newVal), { deep: true })
</script>

<template>
  <div>
    <el-divider content-position="left">
      <span> 结算方式</span>
    </el-divider>
    <el-form-item label="结算方式">
      <div style="width: 10rem;">
        <el-radio-group v-model="modelValue.type">
          <el-radio :value="0">
            定价规则
          </el-radio>
          <el-radio :value="1">
            <template #default>
              <div>
                <span>
                  团购价（当存在未配置团购价的影城时，将使用定价规则中的价格进行结算）</span>
              </div>
            </template>
          </el-radio>
          <el-radio :value="2">
            <template #default>
              <div>
                自定义结算&nbsp;
                <el-select
                  v-model="modelValue.couponPricedBo.pricedType" :max-collapse-tags="8" autocomplete filterable clearable
                  placeholder="请选择结算方式" style="width: 30%;"
                >
                  <el-option label="固定票价" :value="0" />
                  <el-option label="最低票价" :value="1" />
                </el-select>
                {{ modelValue.couponPricedBo.pricedType === 0 ? '=' : '' }}
                {{ modelValue.couponPricedBo.pricedType === 1 ? '+' : '' }}
                <el-input-number
                  v-model="modelValue.couponPricedBo.pricedMoney" :min="0" :max="99999999" :step="0.01"
                  controls-position="right" placeholder="元"
                />
                （单张影票结算价格）
              </div>
            </template>
          </el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
  </div>
</template>
