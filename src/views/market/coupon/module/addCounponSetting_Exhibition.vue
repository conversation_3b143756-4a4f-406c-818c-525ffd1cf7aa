<script setup>
import { defineEmits, defineProps, reactive, ref, watch } from 'vue'
import { listAllExhibitionFilms } from '@/api/exhibition/film'
import { listAllVenue } from '@/api/exhibition/venue'

import exhibitionHallSelector from '@/components/exhibitionHallSelector'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])
// 表单数据
const form = ref({
  exhibitionCinemaScope: 0,
  exhibitionFilmScope: 0,
  exhibitionCinemaIds: [],
  exhibitionFilmIds: [],
  exhibitionCinemaHallIds: [], // Initialize as empty array
  ...props.modelValue,
})
onMounted(() => {
  form.value = props.modelValue
})

// 展览列表
const exhibitionList = ref([])
const exhibitionCinemaList = ref([])

// 表单验证规则
const rules = {
  exhibitionFilmIds: [
    {
      required: true,
      message: '请选择展览',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.value.exhibitionFilmScope !== 0 && (!value || value.length === 0)) {
          callback(new Error('请选择展览'))
        }
        else {
          callback()
        }
      },
    },
  ],
  exhibitionCinemaIds: [
    {
      required: true,
      message: '请选择展览馆',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.value.exhibitionCinemaScope !== 0 && (!value || value.length === 0)) {
          callback(new Error('请选择展览馆'))
        }
        else {
          callback()
        }
      },
    },
  ],
}

// 监听父组件传入的数据变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    Object.assign(form, newVal)
  }
}, { deep: true, immediate: true })

// 监听表单数据变化，同步到父组件
watch(form, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

const exhibitionHallRoomList = ref([]) // 新增影厅列表
watch(() => form.value.exhibitionCinemaIds, async (newVal, oldVal) => {
  if (newVal && newVal.length > 0) {
    try {
      // Ensure exhibitionCinemaHallIds is initialized
      if (!Array.isArray(form.value.exhibitionCinemaHallIds)) {
        form.value.exhibitionCinemaHallIds = []
      }

      const newHalls = []
      newVal.forEach((cinemaId, index) => {
        // Find existing hall data for this cinema
        const existingHallData = Array.isArray(form.value.exhibitionCinemaHallIds)
          ? form.value.exhibitionCinemaHallIds.find(r => r && r.cinemaId === cinemaId)
          : null

        newHalls[index] = {
          cinemaId,
          halls: existingHallData?.halls || [],
        }
      })
      form.value.exhibitionCinemaHallIds = newHalls
    }
    catch (error) {
      console.error('影厅请求失败:', error)
    }
  }
  else {
    // Reset to empty array when no cinemas are selected
    form.value.exhibitionCinemaHallIds = []
    exhibitionHallRoomList.value = []
  }
}, { deep: true })

// 获取展览列表
function getExhibitionList() {
  // 调用API获取展览列表
  listAllExhibitionFilms().then((res) => {
    exhibitionList.value = res.data || []
  })
}

// 新增获取展览馆列表方法
function getExhibitionFilms() {
  listAllVenue().then((res) => {
    exhibitionCinemaList.value = res.data || []
  })
}

onMounted(() => {
  getExhibitionList()
  getExhibitionFilms() // 新增
})
</script>

<template>
  <div class="app-container">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 新增展览馆限制 -->
      <el-form-item label="适用展览馆" prop="exhibitionCinemaScope">
        <el-radio-group v-model="form.exhibitionCinemaScope">
          <el-radio :value="0">
            不限展览馆
          </el-radio>
          <el-radio :value="1">
            指定展览馆
          </el-radio>
          <el-radio :value="2">
            排除展览馆
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        v-if="form.exhibitionCinemaScope === 1 || form.exhibitionCinemaScope === 2" label="选择展览馆"
        prop="exhibitionCinemaIds"
      >
        <el-select v-model="form.exhibitionCinemaIds" multiple filterable placeholder="请选择展览馆" style="width: 100%;">
          <el-option v-for="item in exhibitionCinemaList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.exhibitionCinemaIds && form.exhibitionCinemaIds.length > 0 && form.exhibitionCinemaScope" label="选择展厅">
        <el-card class="box-card" style="width: 100%;" shadow="never">
          <el-row
            v-for="(cinemaId, index) in form.exhibitionCinemaIds" :key="index" style="padding: 5px 0;"
            :gutter="20"
          >
            <el-col :span="6">
              <span style="display: flex;align-items: flex-end; justify-content: center; text-align: center;"><b>{{
                exhibitionCinemaList.find(r => r.id === cinemaId)?.name || 'Unknown Cinema' }}</b></span>
            </el-col>
            <el-col :span="18">
              <!-- Make sure form.exhibitionCinemaHallIds[index] exists before accessing .halls -->
              <exhibitionHallSelector
                v-if="form.exhibitionCinemaHallIds && form.exhibitionCinemaHallIds[index]"
                v-model="form.exhibitionCinemaHallIds[index].halls" :cinema-id="cinemaId"
              />
            </el-col>
          </el-row>
        </el-card>
      </el-form-item>

      <!-- 展览券特有设置 -->
      <el-form-item label="适用展览" prop="exhibitionFilmScope">
        <el-radio-group v-model="form.exhibitionFilmScope">
          <el-radio :value="0">
            不限展览
          </el-radio>
          <el-radio :value="1">
            指定展览
          </el-radio>
          <!--          <el-radio :value="2">排除展览</el-radio> -->
        </el-radio-group>
      </el-form-item>

      <!-- 指定展览或排除展览时显示 -->
      <el-form-item
        v-if="form.exhibitionFilmScope === 1 || form.exhibitionFilmScope === 2" label="选择展览"
        prop="exhibitionFilmIds"
      >
        <el-select v-model="form.exhibitionFilmIds" multiple filterable placeholder="请选择展览" style="width: 100%;">
          <el-option v-for="item in exhibitionList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <!-- 其他展览券特有设置 -->
      <!-- 暂时隐藏适用票档
      <el-form-item label="适用票档" prop="ticketTypeScope">
        <el-radio-group v-model="form.ticketTypeScope">
          <el-radio :label="0">不限票档</el-radio>
          <el-radio :label="1">指定票档</el-radio>
        </el-radio-group>
      </el-form-item>
      -->

      <!-- 其他设置项... -->
      <pre>{{ form }}</pre>
    </el-form>
  </div>
</template>
