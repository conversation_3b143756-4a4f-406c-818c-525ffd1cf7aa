<script setup>
import { getCurrentInstance, ref } from 'vue'
import { listHall } from '@/api/cinemas/hall'
// 组件中声明emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    },
  },
  cinemaId: {
    type: Number,
    default: () => {
      return 0
    },
    request: true,
  },
})
const emit = defineEmits(['onChange'])
const { proxy } = getCurrentInstance()
const halls = ref([])
// 筛选halls的hallType
const hallTypes = ref([])

// 根据影院id列表获取影厅列表
function getHallByIds(cinemaId) {
  listHall({ cinemaId }).then((res) => {
    console.log(res)
    const { code, data } = res
    halls.value = data
    hallTypes.value = [...new Set(halls.value.map(hall => hall.hallType))]
  })
}
getHallByIds(props.cinemaId)

console.log(hallTypes.value)

// // 根据hallType筛选halls
function filterHalls(hallType) {
  return halls.value.filter(hall => hall.hallType === hallType)
}
//
// // 选中的hall
const selectedHallList = ref([])
//
// selectedHallList.value = props.modelValue.map((hallId) => {
//   return props.halls.find((hall) => hall.hallId === hallId);
// });
//
//
// // 更新选中的hall的Arry
function updateHall() {
  // console.log('updateHall',props.cinemaId,selectedHallList.value)
  // emit('onChange',props.cinemaId,selectedHallList.value)
  proxy.$emit('onChange', props.cinemaId, selectedHallList.value)
}
//
//  切换影厅全选状态
function handleCheckAllChange(hallType) {
  const halls = filterHalls(hallType)
  const selectedHalls = selectedHallList.value.filter(
    hall => hall.hallType === hallType,
  )
  if (selectedHalls.length === 0 && selectedHalls.length < halls.length) {
    selectedHallList.value = [
      ...selectedHallList.value.filter(hall => hall.hallType !== hallType),
      ...halls,
    ]
  }
  else {
    // 将要取消全选
    selectedHallList.value = selectedHallList.value.filter(
      hall => hall.hallType !== hallType,
    )
  }
}
//
// // 影厅类型是否全选状态
function isSelectAll(hallType) {
  const halls = filterHalls(hallType)
  const selectedHalls = selectedHallList.value.filter(
    hall => hall.hallType === hallType,
  )
  // console.log(halls.length,selectedHalls.length);
  return halls.length === selectedHalls.length
}
//
// // 该类型影厅是否半选
function isIndeterminate(hallType) {
  const halls = filterHalls(hallType)
  const selectedHalls = selectedHallList.value.filter(
    hall => hall.hallType === hallType,
  )
  if (selectedHalls.length === 0) {
    return false
  }
  return halls.length > selectedHalls.length && selectedHalls.length > 0
}
//
// // 监听selectedHallList变化
watch(selectedHallList, (newVal, oldVal) => {
  console.log('selectedHallList变化', newVal, oldVal)
  updateHall()
})
</script>

<template>
  <!--  {{ selectedHallList }} -->
  <!--  <pre>{{halls}}</pre> -->
  <div
    v-for="(hallType, index) in hallTypes"
    :key="index"
    class="hall-selection"
  >
    <el-row>
      <el-col :span="4">
        <div
          style="
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              margin-left: 0;
              background-color: rgb(245 245 245 / 99%);
"
        >
          <el-checkbox
            :model-value="isSelectAll(hallType)"
            :indeterminate="isIndeterminate(hallType)"
            @change="handleCheckAllChange(hallType)"
          >
            {{ hallType }}
          </el-checkbox>
        </div>
      </el-col>
      <el-col :span="19" style="margin-left: 1vw;">
        <el-checkbox-group v-model="selectedHallList">
          <el-checkbox

            v-for="hall in filterHalls(hallType)"
            :key="hall.hallId"
            :label="hall"
          >
            {{ hall.hallName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.hall-selection {
  // margin-bottom: 10px;
  width: 100%;
  margin-left: -10px;
  border: 1px solid #ebeef5;
}
</style>
