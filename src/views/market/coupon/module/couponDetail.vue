<script name="couponDetail" setup>
import { getCurrentInstance, reactive, ref } from 'vue'
// import { listHall } from '@/api/cinemas/hall'
// import { getGoodsTypeAll } from '@/api/modules/business/goodsType'

// import { couponCopy } from '@/api/modules/market/coupon'
import settings from '@/settings'
import { COUPON_TYPE_OPTIONS, COUPON_TYPES, EXCHANGE_DIFF_TYPE, SERVICE_FEE_REDUCTION, USE_ON_OPTIONS } from './constants'
// import CouponDetail_exhibition from '@/views/activity/coupon/module/couponDetail_exhibition.vue'
// import couponDetailExhibition from './couponDetail_exhibition.vue'

// import couponDetailGoods from './couponDetail_goods.vue'
// import couponDetailTicket from './couponDetail_ticket.vue'

const props = defineProps({
  couponData: {
    type: Object,
    default: null,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits()

const { proxy } = getCurrentInstance()

const couponDetail = ref(props.couponData)
console.log('couponDetail', couponDetail.value);

const {
  couponCanuseConfig,
  couponCodeCreateConfig,
  couponGoodsConfigs,
  couponMainInfo,
  couponSettleMethodConfig,
  couponShowConfigs,
  couponTicketConfigs,
} = couponDetail.value

// useOn枚举值：0-电影票，1-卖品，2-演出票 3-展览券
const useOn = reactive({
  0: '电影票',
  1: '卖品',
  2: '演出票',
  3: '展览券',
})

// 优惠券类型：0-满减，1-减至，2-通况，3-折扣，4-多对一
const couponTypeOptions = reactive({
  0: '满减',
  1: '减至',
  2: '通况',
  3: '折扣',
  4: '多对一',
})

// if (couponDetail.value.periods && couponDetail.value.periods.length) {
//   couponDetail.value.periods.forEach((item) => {
//     item.dayText = item.days.map((day) => dayNames[day - 1]).join("、");
//   });
// }

// 满减0，减至1，通况2，折扣3, 多对一4
const couponType = couponMainInfo.couponType
if (couponType === 0) {
  const priceOrigin = couponMainInfo.couponRules[0].priceOrigin / 100
  const priceReduce = couponMainInfo.couponRules[0].priceReduce / 100
  couponMainInfo.ruleText = `整笔订单满${priceOrigin}元，减${priceReduce}元`
}
else if (couponType === 1) {
  const priceOrigin = couponMainInfo.couponRules[0].priceOrigin / 100
  couponDetail.value.ruleText = `单张影票减至${priceOrigin}元`
}
else if (couponType === 3) {
  const priceOrigin = couponMainInfo.couponRules[0].priceOrigin / 100
  couponDetail.value.ruleText = `整笔订单打${priceOrigin}折`
}

if (couponMainInfo.useOn === 2 && couponShowConfigs?.length) {
  const showGroup = []
  const showInfoIdMap = new Map()
  couponDetail.value.showScheduleList.forEach((item) => {
    const showDataId = item.showInfoId
    let scheduleList = showInfoIdMap.get(showDataId)
    if (!scheduleList) {
      scheduleList = []
      showGroup.push({
        showInfoId: showDataId,
        showName: item.showName,
        scheduleList,
      })
      showInfoIdMap.set(showDataId, scheduleList)
    }

    const timeText = `${item.startTime.slice(0, 16)} - ${item.endTime.slice(
      11,
      16,
    )}`
    scheduleList.push({
      id: item.showScheduleId,
      timeText,
    })
  })
  couponDetail.value.showGroup = showGroup
}
if (couponDetail.value.settleMethod == 2) {
  // 自定义结算
  couponDetail.value.couponPricedBo.pricedMoney
    = couponDetail.value.couponPricedBo.pricedMoney / 100
}

function closeDialog() {
  emit('close')
}

function handleCopy() {
  proxy.$modal
    .confirm('确认复制优惠券？')
    .then(() => {
      loading.value = true

      // return couponCopy({ id: couponDetail.value.id })
    })
    .then(() => {
      proxy.$modal.msgSuccess('已复制')
      emit('close')
      emit('refresh')
    })
}

function handleEdit() {
  emit('close')
  emit('edit', couponDetail.value)
}

function cinemaList(list) {
  return list.reduce((map, item) => {
    const key = item.cinemaId
    const hall = map[key] || {
      cinemaId: key,
      cinemaName: item.cinemaName,
      hallList: [],
    }
    hall.hallList.push(item)
    map[key] = hall
    return map
  }, {})
}

const cinemaHallList = ref([])
const _cinemaList = ref([])

if (couponTicketConfigs) {
  couponTicketConfigs.forEach((item) => {
    // console.log({ item });
    item.couponCinemas.forEach((halls) => {
      const cinema = _cinemaList.value.find(f => f.id === halls.cinemaId)
      // console.log({ cinema });
      if (!cinema) {
        _cinemaList.value.push({
          id: halls.cinemaId,
          name: halls.cinemaName,
        })
      }
    })
  })
}

_cinemaList.value.forEach((item) => {
  // listHall({ cinemaId: item.id }).then((res) => {
  //   console.log(res)
  //   const { code, data } = res
  //   cinemaHallList.value.push({
  //     id: item.id,
  //     name: item.name,
  //     hallList: [...data],
  //   })
  // })
  cinemaHallList.value.push({
    id: item.id,
    name: item.name,
    hallList: [],
  })
})

console.log({ cinemaHallList })

// const getHallByIds = (hallId) => {
//   return cinemaHallList.value.find(f => f.id === hallId)
// }
// console.log({ couponTicketConfigs });

// console.log(cinemaList(couponTicketConfigs.couponCinemas));
</script>

<template>
  <el-dialog
    v-model="props.modelValue"
    destroy-on-close
    width="90%"
    @close="closeDialog"
  >
    <template #header>
      <el-text v-if="couponDetail.couponMainInfo.useOn === 0" size="large">
        电影票优惠券
      </el-text>
      <el-text v-if="couponDetail.couponMainInfo.useOn === 1" size="large">
        卖品优惠券
      </el-text>
      <el-text v-if="couponDetail.couponMainInfo.useOn === 2" size="large">
        演出票优惠券
      </el-text>
      <el-text v-if="couponDetail.couponMainInfo.useOn === 3" size="large">
        展览券优惠券
      </el-text>
    </template>
    <el-card shadow="never">
      <el-descriptions :column="1" title="优惠券信息">
        <el-descriptions-item
          label="优惠券ID："
          label-align="right"
          label-class-name="my-label"
        >
          {{ couponDetail.couponMainInfo.id }}
        </el-descriptions-item>
        <el-descriptions-item
          label="优惠券名称："
          label-align="right"
          label-class-name="my-label"
        >
          {{ couponDetail.couponMainInfo.name }}
        </el-descriptions-item>
        <el-descriptions-item
          label="适用商品类型："
          label-align="right"
          label-class-name="my-label"
        >
          <el-tag>
            {{
              USE_ON_OPTIONS.find(
                (item) => item.value === couponDetail.couponMainInfo.useOn,
              )?.label
            }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          label="优惠券类型："
          label-align="right"
          label-class-name="my-label"
        >
          {{
            COUPON_TYPE_OPTIONS.find(
              (item) => item.value === couponDetail.couponMainInfo.couponType,
            )?.label
          }}
        </el-descriptions-item>
        <el-descriptions-item
          label="优惠规则："
          label-align="right"
          label-class-name="my-label"
        >
          <span style="display: inline-block;">
            <!-- 满减 0 -->
            <span v-if="couponDetail.couponMainInfo.couponType === COUPON_TYPES.FULL_REDUCTION">
              满{{
                (
                  couponDetail.couponMainInfo.couponRules[0].priceOrigin / 100
                ).toFixed(2)
              }}
              元 减{{
                (
                  couponDetail.couponMainInfo.couponRules[0].priceReduce / 100
                ).toFixed(2)
              }}
              元
            </span>
            <!-- 减至 1 -->
            <span v-if="couponDetail.couponMainInfo.couponType === COUPON_TYPES.REDUCE_TO">
              减至
              <el-tag type="danger">
                {{
                  (
                    couponDetail.couponMainInfo.couponRules[0].priceOrigin / 100
                  ).toFixed(2)
                }}
                元
              </el-tag>
            </span>
            <!-- 通兑 2 -->
            <span v-if="couponDetail.couponMainInfo.couponType === COUPON_TYPES.EXCHANGE">
              <div
                v-for="(item, index) in couponDetail.couponMainInfo.couponRules"
                :key="item.id"
              >
                <!-- {{ item }} -->
                <span v-if="item.matchRule === EXCHANGE_DIFF_TYPE.AMOUNT_DIFF || item.matchRule === null">
                  <!-- {{ item }} -->
                  规则{{ index + 1 }}：{{
                    useOn[couponDetail.couponMainInfo.useOn]

                  }}
                  &gt; {{ (item.priceOrigin / 100).toFixed(2) }} 元 ≤
                  {{ (item.priceReduce / 100).toFixed(2) }} 元，补差
                  {{ (item.priceDiff / 100).toFixed(2) }} 元
                </span>
                <span v-if="item.matchRule === EXCHANGE_DIFF_TYPE.LOWEST_PRICE_DEDUCT">
                  <!-- 规则{{ index + 1 }}：{{ useOn[couponDetail.couponMainInfo.useOn] }} &gt; {{ (item.priceOrigin / 100).toFixed(2) }} 元
                  ≤ {{ (item.priceReduce / 100).toFixed(2) }} 元，按最低票价抵扣 -->
                  低于等于<el-tag type="danger">{{
                    (item.priceDiff / 100).toFixed(2)
                  }}</el-tag>元，按最低票价抵扣，超出部分不抵扣
                </span>
              </div>
            </span>

            <!-- 折扣 3 -->
            <span v-if="couponDetail.couponMainInfo.couponType === COUPON_TYPES.DISCOUNT">
              {{
                (
                  couponDetail.couponMainInfo.couponRules[0].priceOrigin / 100
                ).toFixed(2)
              }}
              折
            </span>
            <!-- 多对一 4 -->
            <span v-if="couponDetail.couponMainInfo.couponType === COUPON_TYPES.MULTI_EXCHANGE">
              <div
                v-for="(item, index) in couponDetail.couponMainInfo.couponRules"
                :key="item.id"
                style="margin-bottom: 5px;"
              >
                <!-- {{ item }} -->
                规则{{ index + 1 }}：<el-tag type="danger">
                  {{ item.priceReduce }} 张
                </el-tag>
                券兑换一张 {{ useOn[couponDetail.couponMainInfo.useOn] }}
              </div>
            </span>
          </span>
        </el-descriptions-item>
        <el-descriptions-item
          label="票时代服务费："
          label-align="right"
          label-class-name="my-label"
        >
          <!-- reduction服务费减免：减免0，不减免1 -->
          <el-tag
            v-if="couponDetail.couponMainInfo.reduction === SERVICE_FEE_REDUCTION.REDUCE"
            type="success"
          >
            减免
          </el-tag>
          <el-tag v-else type="danger">
            不减免，服务费<b>{{
              (couponDetail.couponMainInfo.userServiceFee / 100).toFixed(2)
            }}元</b>
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          label="可用渠道："
          label-align="right"
          label-class-name="my-label"
        >
          <el-tag
            v-for="item in couponDetail.couponMainInfo.couponChannels"
            :key="item.id"
            type="success"
          >
            {{ item.channelName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          label="其他说明："
          label-align="right"
          label-class-name="my-label"
        >
          <span style="display: inline-block; width: 70%;">{{
            couponDetail.couponMainInfo.memo
          }}</span>
        </el-descriptions-item>
        <!--        券价值 -->

        <el-descriptions-item
          label="优惠券价值："
          label-align="right"
          label-class-name="my-label"
        >
          <span style="display: inline-block; width: 70%;">
            <div>
              {{ (couponDetail.couponMainInfo.valuable / 100).toFixed(2) }}元
            </div>
          </span>
        </el-descriptions-item>
        <el-descriptions-item
          label="备注："
          label-align="right"
          label-class-name="my-label"
        >
          <span style="display: inline-block; width: 70%;">
            <div>
              {{ couponDetail.couponMainInfo.remark }}
            </div>
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 有效期 -->
    <el-card shadow="never" style="margin-top: 24px;">
      <!-- validScope有效期类型：0时间范围，1限时有效 -->
      <el-descriptions :column="1" title="有效期">
        <el-descriptions-item
          label="有效期类型："
          label-align="right"
          label-class-name="my-label"
        >
          {{
            couponCanuseConfig.validScope === 0 ? "时间范围" : "绑定后N天内可用"
          }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="couponCanuseConfig.validScope === 0"
          label="优惠券有效期："
          label-align="right"
          label-class-name="my-label"
        >
          {{ couponCanuseConfig.startTime }} 至 {{ couponCanuseConfig.endTime }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="couponCanuseConfig.validScope === 1"
          label="优惠券有效期："
          label-align="right"
          label-class-name="my-label"
        >
          绑定后<el-tag>{{ couponCanuseConfig.overdueDay }}</el-tag>天内可使用
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 适用条件-电影票   -->
    <coupon-detail-ticket
      v-if="couponDetail.couponMainInfo.useOn === 0"
      :cinema-list="cinemaList"
      :coupon-detail="couponDetail"
      :cinema-hall-list="cinemaHallList"
    />

    <!-- 适用条件-卖品 -->
    <coupon-detail-goods
      v-if="couponDetail.couponMainInfo.useOn === 1"
      :cinema-list="cinemaList"
      :coupon-detail="couponDetail"
    />

    <coupon-detail_exhibition
      v-if="couponDetail.couponMainInfo.useOn === 3"
      :cinema-list="cinemaList"
      :coupon-detail="couponDetail"
    />

    <el-card shadow="never" style="margin-top: 24px;">
      <el-descriptions :column="1" title="券生成方式">
        <el-descriptions-item
          label="生成方式："
          label-align="right"
          label-class-name="my-label"
        >
          <span style="display: inline-block; width: 70%;">
            <!-- generateType 生成方式：0按需生成，1指定数量 -->
            <el-text
              v-if="couponDetail.couponCodeCreateConfig.generateType"
              type="primary"
            >
              一次性生成 {{ couponDetail.couponCodeCreateConfig.num }}张
            </el-text>
            <el-text v-else type="danger">需要时生成,最多生成{{
              couponDetail.couponCodeCreateConfig.num
            }}张</el-text>
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card
      v-if="couponDetail.couponMainInfo.useOn === 0"
      shadow="never"
      style="margin-top: 24px;"
    >
      <el-descriptions :column="1" title="结算方式">
        <el-descriptions-item
          label="结算方式："
          label-align="right"
          label-class-name="my-label"
        >
          <span style="display: inline-block; width: 70%;">
            <el-tag
              v-if="couponDetail.couponSettleMethodConfig.settleMethod === 0"
            >定价规则</el-tag>
            <el-tag
              v-if="couponDetail.couponSettleMethodConfig.settleMethod === 1"
            >影院团购价</el-tag>
            <el-tag
              v-if="couponDetail.couponSettleMethodConfig.settleMethod === 2"
            >
              自定义结算 -
              {{
                couponDetail.couponSettleMethodConfig.couponPriced.pricedType
                  ? "最低票价"
                  : "固定价格"
              }}
              {{
                couponDetail.couponSettleMethodConfig.couponPriced
                  .pricedType === 0
                  ? "="
                  : ""
              }}
              {{
                couponDetail.couponSettleMethodConfig.couponPriced
                  .pricedType === 1
                  ? "+"
                  : ""
              }}
              {{
                couponDetail.couponSettleMethodConfig.couponPriced.pricedMoney
              }}元
            </el-tag>
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <template #footer>
      <div style="text-align: left;">
        <el-button type="primary" @click="closeDialog">
          返回
        </el-button>
        <el-button @click="handleCopy">
          复制
        </el-button>
        <el-button @click="handleEdit">
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.my-label) {
  display: inline-block;
  width: 30%;
  margin-right: 0;
  vertical-align: top;
  text-align: right;
}

.hall-label {
  min-width: 200px;
  margin-right: 24px;
}

.showgroup-wrap {
  display: inline-flex;
  flex-direction: column;
  margin-bottom: -8px;

  .showgroup-item {
    display: flex;
    width: 800px;

    .groupname {
      margin-right: 8px;
      white-space: nowrap;
    }
  }

  .showgroup-item:not(:first-child) {
    margin-top: 8px;
  }
}
</style>
