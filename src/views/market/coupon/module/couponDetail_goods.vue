<script setup>
import { getCurrentInstance, reactive, ref } from 'vue'
import { getGoodsTypeAll } from '@/api/business/goodsType'

const props = defineProps({
  couponDetail: {
    type: Object,
    default: null,
  },
  cinemaList: {
    type: Function,
    default: null,
  },
})
const { proxy } = getCurrentInstance()
const { bus_film_type } = proxy.useDict('bus_film_type')

const goodsTypeList = ref([])
function getGoodsType() {
  getGoodsTypeAll().then((res) => {
    goodsTypeList.value = res.data
  })
}
getGoodsType()

// useOn枚举值：0-电影票，1-卖品，2-演出票
const useOn = reactive({
  0: '电影票',
  1: '卖品',
  2: '演出票',
})
const dayNames = reactive({
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
})

// 按照数组某一个字段进行分组，返回一个map
function groupBy(list, key) {
  return list.reduce((map, item) => {
    const k = item[key]
    const collection = map[k] || []
    collection.push(item)
    map[k] = collection
    return map
  }, {})
}

const goodsConfigDetail = ref(props.couponDetail.couponGoodsConfigs)
</script>

<template>
  <!-- 适用条件-电影票 -->

  <el-card v-if="couponDetail.couponMainInfo.useOn === 1" shadow="never" style="margin-top: 24px;">
    <template #header>
      <el-text size="large">
        {{ useOn[couponDetail?.couponMainInfo?.useOn || ''] }}适用条件
      </el-text>
    </template>
    <el-descriptions
      v-for="(goodsConfig, index) in couponDetail.couponGoodsConfigs" :key="goodsConfigDetail.id"
      :column="1" :title="`适用条件${index + 1}`"
    >
      <el-descriptions-item label="影院限制类型：" label-align="right" label-class-name="my-label">
        <!-- <el-tag v-if="goodsConfig.couponGoodsSelectScopeConfig.cinemaScope === 0" type="success">不限影院</el-tag>
        <el-tag v-else-if="goodsConfig.couponGoodsSelectScopeConfig.cinemaScope === 1" type="success">指定影院</el-tag>
        <el-tag v-else-if="goodsConfig.couponGoodsSelectScopeConfig.cinemaScope === 2" type="danger">排除影院</el-tag> -->
        <!-- 影院范围：全部0，指定1，排除2 -->
      </el-descriptions-item>
      <pre>
        {{ goodsConfig.couponGoodsSelectScopeConfig }}
      </pre>
      <!-- <el-descriptions-item v-if="goodsConfig.couponGoodsSelectScopeConfig.cinemaScope > 0 &&
    goodsConfig.couponCinemas.length
    " label="影院范围：" label-align="right" label-class-name="my-label">
        <div style="display: inline-block; width: 70%">
          <div>
            以下影院{{
    goodsConfigDetail.cinemaScope === 1 ? "可" : "不可"
  }}用
          </div>
          <!-- <el-card style="margin-top: 12px"> -->
      <!-- <template v-for="cinema in cinemaList(goodsConfig.couponCinemas)" :key="cinema.cinemaId">
            <el-tag class="hall-label" style="margin: 2px;" type="primary">{{ cinema.cinemaName }}</el-tag>
          </template> -->
      <!-- </el-card> -->
      <!-- </div> -->
      <!-- </el-descriptions-item> -->
      <pre>
        {{ goodsConfig.couponGoodsSelectScopeConfig }}
       </pre>
      <!-- <el-descriptions-item label="适用卖品类型：" label-align="right" label-class-name="my-label">
        <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 0" size="large"
          style="margin-left: 1vw" type="primary">
          全部
        </el-text>
        <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 1" size="large"
          style="margin-left: 1vw" type="primary">
          指定分类
        </el-text>
        <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 2" size="large"
          style="margin-left: 1vw" type="primary">
          指定卖品
        </el-text>
      </el-descriptions-item>
      <el-descriptions-item v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 1" label="指定卖品分类："
        label-align="right" label-class-name="my-label">
         {{ goodsTypeList }}
        <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsTypeScope === 0" style="margin-left: 1vw">
          全部
        </el-text>
        <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsTypeScope === 1" style="margin-left: 1vw">
          可用分类： <el-tag v-for="item in goodsConfig.couponGoodsTypes" :key="item.couponId" effect="dark"
            style="margin-left: 1vw" type="success">
            {{ goodsTypeList.find((i) => i.id === item.goodsTypeId)?.typeName || '' }}
          </el-tag>
        </el-text>
        <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsTypeScope === 2" style="margin-left: 1vw">
          不可用分类： <el-tag v-for="item in goodsConfig.couponGoodsTypes" :key="item.couponId" effect="dark"
            style="margin-left: 1vw" type="danger">
            {{ goodsTypeList.find((i) => i.id === item.goodsTypeId)?.typeName || '' }}
          </el-tag>
        </el-text>
      </el-descriptions-item> -->
      <el-descriptions-item v-if="goodsConfigDetail" label="指定卖品：" label-align="right" label-class-name="my-label">
        <!-- goodsInfoScope 适用卖品: 全部0，指定1，排除2 -->
        <el-card body-style="padding: 0;" shadow="never" style=" float: right;width: 70%;">
          <template #header>
            <!-- <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsInfoScope === 0" size="large"
              style="margin-left: 1vw">
              全部卖品可用
            </el-text>
            <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsInfoScope === 1" size="large"
              style="margin-left: 1vw" type="primary">
              可用卖品：
            </el-text>
            <el-text v-if="goodsConfig.couponGoodsSelectScopeConfig.goodsInfoScope === 2" size="large"
              style="margin-left: 1vw" type="warning">
              不可用卖品：
            </el-text> -->
          </template>
          <div
            v-for="(item, index) in groupBy(goodsConfig.couponGoodsInfos, 'cinemaName')" :key="index"
            style=" margin-bottom: 10px;border-bottom: 1px dashed #dcdfe6;"
          >
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <el-text
                class="hall-label"
                style=" display: flex; align-items: center; justify-content: center;margin-right: 10px;"
                type="primary"
              >
                {{
                  item[0].cinemaName }}
              </el-text>
              <div style="display: flex; flex-wrap: wrap;">
                <el-tag
                  v-for="goods in item" :key="goods.id" class="hall-label" size="small" style="margin: 2px;"
                  type="success"
                >
                  {{ goods.goodsName }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>
