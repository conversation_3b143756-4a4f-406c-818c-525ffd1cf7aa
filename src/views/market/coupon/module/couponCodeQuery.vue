<script setup>
import { defineProps, reactive, ref } from 'vue'
import { getCodeList } from '@/api/modules/business/couponCode'
import { stepCouponInfoList } from '@/api/modules/market/coupon'

const props = defineProps({
  channelList: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['close'])

const queryForm = reactive({
  code: '',
  useOn: '',
  channelId: '',
})

const couponInfo = ref(null)
const errorMsg = ref('')
const isSearching = ref(false)
const searchProgress = ref(0)
const progressStatus = ref('active')
const currentScanning = ref('')
const scannedCount = ref(0)
const totalCount = ref(0)
const searchStartTime = ref(0)
const searchEndTime = ref(0)
const searchDuration = ref(0)

const statusMap = {
  0: { text: '未绑定', type: 'warning' },
  1: { text: '已绑定用户', type: 'success' },
  2: { text: '用户已成功使用', type: 'info' },
  3: { text: '用户使用失败', type: 'info' },
  4: { text: '用户未绑定过期', type: 'info' },
  5: { text: '用户过期未使用', type: 'info' },
  6: { text: '已作废', type: 'info' },
}

function getStatusText(status) {
  return statusMap[status]?.text || '未知状态'
}

function getStatusType(status) {
  return statusMap[status]?.type || 'info'
}

function progressFormat(percentage) {
  return `${percentage}%`
}

async function handleSearch() {
  if (!queryForm.code) {
    errorMsg.value = '请输入券码'
    return
  }
  isSearching.value = true
  errorMsg.value = ''
  couponInfo.value = null
  searchProgress.value = 0
  progressStatus.value = 'active'
  scannedCount.value = 0
  searchStartTime.value = Date.now()
  searchEndTime.value = 0
  searchDuration.value = 0

  try {
    await performSearch()
  }
  catch (error) {
    console.error('券码查询失败:', error)
    errorMsg.value = error.message || '查询失败，请稍后重试'
    progressStatus.value = 'exception'
  }
  finally {
    isSearching.value = false
    searchEndTime.value = Date.now()
    searchDuration.value = Math.round(
      (searchEndTime.value - searchStartTime.value) / 1000,
    )
  }
}

async function performSearch() {
  let isAborted = false
  // 第一步：分页获取所有启用状态的优惠券列表
  currentScanning.value = '正在获取启用状态的优惠券列表...'
  progressStatus.value = 'active'
  const allCoupons = []
  let pageNum = 1
  const pageSize = 10
  let total = 0
  let totalPages = 1
  try {
    while (true) {
      if (isAborted) { return }
      // 分页加载进度提示
      if (pageNum === 1) {
        currentScanning.value = `正在加载第1页...`
      }
      else {
        currentScanning.value = `正在加载第${pageNum}页/共${totalPages}页...`
      }
      // 每页请求加超时（5秒）
      const pagePromise = stepCouponInfoList({
        pageNum,
        pageSize, // 每次查10条
        useStatus: 1, // 只查询启用状态的券
        isAsc: 'desc',
        orderByColumn: 'id',
        ...(queryForm.useOn !== '' ? { useOn: queryForm.useOn } : {}),
        ...(queryForm.channelId !== ''
          ? { channelId: queryForm.channelId }
          : {}),
      })
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`第${pageNum}页加载超时`)), 10000),
      )
      let couponListRes
      try {
        couponListRes = await Promise.race([pagePromise, timeoutPromise])
      }
      catch (e) {
        isAborted = true
        throw e
      }
      if (couponListRes.code !== 200) {
        isAborted = true
        throw new Error('获取优惠券列表失败')
      }
      const couponList = couponListRes.rows || []
      if (pageNum === 1) {
        total = couponListRes.total || couponList.length
        totalPages = Math.ceil(total / pageSize) || 1
      }
      allCoupons.push(...couponList)
      // 分页加载进度百分比
      searchProgress.value = Math.round(
        (Math.min(pageNum, totalPages) / totalPages) * 100,
      )
      if (allCoupons.length >= total || couponList.length < pageSize) {
        break
      }
      pageNum++
      // 小延迟，避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    totalCount.value = allCoupons.length
    if (totalCount.value === 0) {
      isAborted = true
      throw new Error('没有找到任何启用状态的优惠券')
    }
    // 加载完券列表，切换进度条为success（绿色）
    progressStatus.value = 'success'
    // 第二步：遍历每个启用状态的券，查询其券码列表
    for (let i = 0; i < allCoupons.length; i++) {
      if (isAborted) { return }
      const coupon = allCoupons[i]
      currentScanning.value = `正在扫描：${coupon.name}`
      scannedCount.value = i + 1
      searchProgress.value = Math.round(((i + 1) / totalCount.value) * 100)
      try {
        // 根据couponId和券码查询券码信息
        const codeListRes = await getCodeList({
          couponId: coupon.id,
          code: queryForm.code,
          pageNum: 1,
          pageSize: 10,
        })
        if (
          codeListRes.code === 200
          && codeListRes.rows
          && codeListRes.rows.length > 0
        ) {
          // 找到目标券码，立即停止查询
          const foundCode = codeListRes.rows[0]
          couponInfo.value = {
            ...foundCode,
            id: coupon.id,
            name: coupon.name,
            couponType: coupon.couponType,
            useOn: coupon.useOn,
            validStartTime: coupon.couponCanuseConfig?.startTime,
            validEndTime: coupon.couponCanuseConfig?.endTime,
          }
          searchProgress.value = 100
          currentScanning.value = `已找到券码，停止查询`
          break // 立即跳出循环，停止查询
        }
      }
      catch (error) {
        isAborted = true
        console.warn(`查询券 ${coupon.name} 的券码失败:`, error)
        throw error
      }
      // 添加小延迟，避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    if (!couponInfo.value && !isAborted) {
      errorMsg.value = `未找到券码：${queryForm.code}（仅在启用状态的优惠券中查找）`
    }
  }
  catch (e) {
    // 外层catch，异常已处理，isAborted已设true
    throw e
  }
}

function resetSearch() {
  queryForm.code = ''
  queryForm.useOn = ''
  queryForm.channelId = ''
  couponInfo.value = null
  errorMsg.value = ''
  searchProgress.value = 0
  progressStatus.value = 'active'
  currentScanning.value = ''
  scannedCount.value = 0
  totalCount.value = 0
  searchStartTime.value = 0
  searchEndTime.value = 0
  searchDuration.value = 0
}

function closeDialog() {
  emit('close')
}

defineExpose({
  queryByCode: (code) => {
    queryForm.code = code
    handleSearch()
  },
})
</script>

<template>
  <el-dialog
    width="800px"
    title="券码查询"
    destroy-on-close
    @close="closeDialog"
  >
    <!-- 查询输入区域 -->
    <div v-if="!isSearching && !couponInfo">
      <el-form :model="queryForm" label-width="100px">
        <el-form-item label="券码">
          <el-input
            v-model="queryForm.code"
            placeholder="请输入券码"
            clearable
            style="width: 300px;"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="适用商品类型">
          <el-select
            v-model="queryForm.useOn"
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option label="电影票" :value="0" />
            <el-option label="卖品" :value="1" />
            <el-option label="演出" :value="2" />
            <el-option label="展览" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select
            v-model="queryForm.channelId"
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in props.channelList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="isSearching"
            @click="handleSearch"
          >
            开始查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 搜索进度 -->
    <div v-if="isSearching" class="search-progress">
      <el-progress
        :percentage="searchProgress"
        :format="progressFormat"
        :status="progressStatus"
      />
      <div class="progress-info">
        <p>正在扫描启用状态的优惠券列表...</p>
        <p>当前扫描：{{ currentScanning }}</p>
        <p>已扫描：{{ scannedCount }} / {{ totalCount }}</p>
      </div>
    </div>

    <!-- 查询结果 -->
    <div v-if="couponInfo && !isSearching">
      <!-- 查询统计信息 -->
      <el-alert
        :title="`查询完成！扫描了 ${scannedCount} 个优惠券，耗时 ${searchDuration} 秒`"
        type="success"
        show-icon
        style="margin-bottom: 20px;"
      />

      <el-descriptions title="券码查询结果" :column="2" border>
        <el-descriptions-item label="优惠券ID">
          {{
            couponInfo.id
          }}
        </el-descriptions-item>
        <el-descriptions-item label="优惠券名称">
          {{
            couponInfo.name
          }}
        </el-descriptions-item>
        <el-descriptions-item label="券码">
          {{
            couponInfo.code
          }}
        </el-descriptions-item>
        <el-descriptions-item label="券码状态">
          <el-tag :type="getStatusType(couponInfo.status)">
            {{ getStatusText(couponInfo.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="优惠券类型">
          <el-tag v-if="couponInfo.couponType === 0" type="success">
            满减券
          </el-tag>
          <el-tag v-else-if="couponInfo.couponType === 1" type="warning">
            减至券
          </el-tag>
          <el-tag v-else-if="couponInfo.couponType === 2">
            通兑券
          </el-tag>
          <el-tag v-else-if="couponInfo.couponType === 3" type="danger">
            折扣券
          </el-tag>
          <el-tag v-else-if="couponInfo.couponType === 4" type="info">
            多兑一券
          </el-tag>
          <span v-else>{{ couponInfo.couponType }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="适用商品">
          <el-tag v-if="couponInfo.useOn === 0" type="success">
            电影票
          </el-tag>
          <el-tag v-else-if="couponInfo.useOn === 1" type="primary">
            卖品
          </el-tag>
          <el-tag v-else-if="couponInfo.useOn === 2" type="warning">
            演出
          </el-tag>
          <el-tag v-else-if="couponInfo.useOn === 3" type="info">
            展览
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="绑定用户手机号">
          {{
            couponInfo.mobile || "--"
          }}
        </el-descriptions-item>
        <el-descriptions-item label="票时代订单号">
          {{
            couponInfo.orderNo || "--"
          }}
        </el-descriptions-item>
        <el-descriptions-item label="券码生成时间">
          {{
            couponInfo.generateTime || "--"
          }}
        </el-descriptions-item>
        <el-descriptions-item label="券码绑定时间">
          {{
            couponInfo.bindTime || "--"
          }}
        </el-descriptions-item>
        <el-descriptions-item label="券码使用时间">
          {{
            couponInfo.useTime || "--"
          }}
        </el-descriptions-item>
        <el-descriptions-item label="优惠券有效期">
          {{ couponInfo.validStartTime || "--" }} 至
          {{ couponInfo.validEndTime || "--" }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMsg && !isSearching" class="error-message">
      <el-alert :title="errorMsg" type="error" show-icon />
    </div>

    <template #footer>
      <el-button @click="closeDialog">
        关闭
      </el-button>
      <el-button
        v-if="couponInfo && !isSearching"
        type="primary"
        @click="resetSearch"
      >
        重新查询
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.search-progress {
  padding: 20px;
  text-align: center;
}

.progress-info {
  margin-top: 20px;
  text-align: left;
}

.progress-info p {
  margin: 8px 0;
  color: #666;
}

.error-message {
  padding: 20px;
  text-align: center;
}
</style>
