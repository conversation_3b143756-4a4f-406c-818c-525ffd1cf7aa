<script setup name="couponCode">
import { ElLoading } from 'element-plus'
import { defineProps, onMounted, reactive, ref } from 'vue'
import { bindBatch, bindCompUser, bindOne, delCode, getCodeList } from '@/api/modules/market/coupon'
import selectCompanyUserComp from './selectCompanyUserComp.vue'

const props = defineProps({
  couponData: {
    type: Object,
    default: {},
  },
})
const emit = defineEmits()
const { proxy } = getCurrentInstance()

const codeList = ref([])
const queryParams = reactive({
  couponId: props.couponData.id,
  code: '',
  status: '',
  mobile: '',
  orderId: '',
  useOn: '',
  pageNum: 1,
  pageSize: 10,
})
const total = ref(0)
const tableLoading = ref(true)

onMounted(() => {
  getList()
})

function getList() {
  getCodeList(queryParams).then((res) => {
    tableLoading.value = false
    codeList.value = res.rows
    total.value = res.total
  })
}

const statusMap = {
  0: {
    text: '未绑定',
    bindDisable: false,
    cancelDisable: false,
    type: 'warning',
  },
  1: {
    text: '已绑定用户',
    bindDisable: true,
    cancelDisable: false,
    type: 'success',
  },
  2: {
    text: '用户已成功使用',
    bindDisable: true,
    cancelDisable: true,
    type: 'info',
  },
  3: {
    text: '用户使用失败',
    bindDisable: true,
    cancelDisable: false,
    type: 'info',
  },
  4: {
    text: '用户未绑定过期',
    bindDisable: true,
    cancelDisable: false,
    type: 'info',
  },
  5: {
    text: '已过期',
    bindDisable: false,
    cancelDisable: false,
    type: 'info',
  },
  6: {
    text: '已作废',
    bindDisable: true,
    cancelDisable: true,
    type: 'info',
  },
}
function getStatus(val, type) {
  if (val == null) { return }
  if (type == 'text') {
    return statusMap[val].text
  }
  return statusMap[val][type]
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}
let downloadLoadingInstance = null

function handleExport() {
  downloadLoadingInstance = ElLoading.service({ text: '正在下载数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' })
  proxy.download('/business/coupon/code/export', { ...queryParams }, `券码列表${new Date().getTime()}.xls`).then((res) => {
    downloadLoadingInstance.close()
  })
}

const bindDialog = reactive({
  show: false,
  mobile: '',
  id: '',
})

function showBind(data) {
  bindDialog.mobile = ''
  bindDialog.id = data.id
  bindDialog.show = true
}
function handleBind() {
  if (!bindDialog.mobile) {
    proxy.$message.error('请输入手机号码')
    return
  }
  bindOne({ id: bindDialog.id, mobile: bindDialog.mobile }).then((res) => {
    proxy.$message.success('绑定成功')
    bindDialog.show = false
    getList()
  })
}

const batchBindDialog = reactive({
  show: false,
  couponId: props.couponData.id,
  mobiles: '',
  type: 0,
  num: 1,
  selectGroups: [],
})
function showBatchBind(data) {
  batchBindDialog.show = true
  batchBindDialog.mobiles = ''
  batchBindDialog.num = 1
  batchBindDialog.type = 0
  batchBindDialog.selectGroups = []
}
function handleBatchBind() {
  if (batchBindDialog.type === 0) {
    if (!batchBindDialog.mobiles) {
      proxy.$message.error('请输入手机号码')
      return
    }
    const batchParams = {
      ...batchBindDialog,
      mobiles: batchBindDialog.mobiles.split('\n'),
    }
    delete batchParams.show
    bindBatch(batchParams).then((res) => {
      proxy.$message.success('绑定成功')
      batchBindDialog.show = false
      getList()
    })
  }
  if (batchBindDialog.type === 1) {
    if (!batchBindDialog.selectGroups.length) {
      proxy.$message.error('请选择指定用户')
      return
    }
    const userIds = []
    batchBindDialog.selectGroups.forEach((group) => {
      userIds.push(...group.userIds)
    })
    const batchParams = {
      couponId: batchBindDialog.couponId,
      userIds,
      num: batchBindDialog.num,
    }
    bindCompUser(batchParams).then((res) => {
      proxy.$message.success('绑定成功')
      batchBindDialog.show = false
      getList()
    })
  }
}

const codeTable = ref(null)
function handleBatchCancel() {
  const rows = codeTable.value.getSelectionRows()
  if (!rows.length) {
    proxy.$message.warning('请选择要操作的券码')
    return
  }

  const ids = rows.map(item => item.id)
  proxy.$modal.confirm('作废后，这批券将不能使用；用户端将看到此券已作废。', '确认要作废这批券码吗？').then(() => {
    return delCode(ids)
  }).then(() => {
    proxy.$modal.msgSuccess('操作成功')
    getList()
  }).catch(() => {})
}

function handleCancel(data) {
  delCode(data.id).then((res) => {
    proxy.$message.success('删除成功')
    getList()
  })
}

function closeDialog() {
  emit('close')
}

const selectUserShow = ref(false)
function handleSelectUser(data) {
  const compIndex = batchBindDialog.selectGroups.findIndex(item => item.compId === data.compId)
  if (compIndex === -1) {
    batchBindDialog.selectGroups.push(data)
  }
  else {
    batchBindDialog.selectGroups[compIndex].userIds = data.userIds
  }
  selectUserShow.value = false
}
</script>

<template>
  <el-dialog width="70%" title="查看券码" destroy-on-close @close="closeDialog">
    <div>
      <el-row :gutter="20">
        <el-col :span="6">
          优惠券ID：{{ couponData.id }}
        </el-col>
        <el-col :span="6">
          优惠券名称：{{ couponData.name }}
        </el-col>
        <el-col :span="6">
          优惠券类型：
          <el-tag v-if="couponData.couponType === 0">
            满减券
          </el-tag>
          <el-tag v-if="couponData.couponType === 1">
            减至券
          </el-tag>
          <el-tag v-if="couponData.couponType === 2">
            通兑券
          </el-tag>
          <el-tag v-if="couponData.couponType === 3">
            折扣券
          </el-tag>
          <el-tag v-if="couponData.couponType === 4">
            多兑一券
          </el-tag>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 12px;">
        <el-col :span="6">
          优惠券使用状态： {{ couponData.couponCodeCreateConfig.useStatus === 1 ? '启用' : '停用' }}
        </el-col>
        <el-col :span="12">
          优惠券有效日期： {{ couponData.couponCanuseConfig.startTime }} - {{ couponData.couponCanuseConfig.endTime }}
        </el-col>
      </el-row>
    </div>
    <div style="margin-top: 36px;margin-bottom: 20px;">
      <el-form ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="券码：" prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="券码状态：" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" style="width: 200px;">
            <el-option label="全部" value="" />
            <el-option label="未绑定用户" :value="0" />
            <el-option label="已绑定用户" :value="1" />
            <el-option label="用户已成功使用" :value="2" />
            <el-option label="用户使用失败" :value="3" />
            <el-option label="用户未绑定过期" :value="4" />
            <el-option label="用户过期未使用" :value="5" />
            <el-option label="作废" :value="6" />
          </el-select>
        </el-form-item>
        <el-form-item label="绑定用户手机号：" prop="mobile">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="票时代订单号：" prop="orderId">
          <el-input
            v-model="queryParams.orderId"
            placeholder="请输入"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            查询
          </el-button>
          <el-button @click="showBatchBind">
            批量绑定用户
          </el-button>
          <el-button @click="handleBatchCancel">
            批量作废
          </el-button>
          <el-button @click="handleExport">
            批量导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table ref="codeTable" v-loading="tableLoading" :data="codeList">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="券码ID" />
      <el-table-column prop="code" label="券码" />
      <el-table-column prop="status" label="券码状态">
        <template #default="{ row }">
          <el-tag :type="getStatus(row.status, 'type')">
            {{ getStatus(row.status, 'text') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="绑定用户手机号" />
      <el-table-column prop="orderNo" label="票时代订单号" />
      <el-table-column prop="generateTime" label="券码生成时间" width="200" />
      <el-table-column prop="bindTime" label="券码绑定用户时间" width="200" />
      <el-table-column prop="useTime" label="券码使用时间" width="200" />
      <el-table-column label="操作" fixed="right" width="140" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button
            v-if="!getStatus(row.status, 'bindDisable')"
            type="primary"
            link
            @click="showBind(row)"
          >
            绑定用户
          </el-button>
          <el-button v-else type="info" link disabled>
            绑定用户
          </el-button>
          <el-popconfirm
            v-if="!getStatus(row.status, 'cancelDisable')"
            title="确认要作废这张券码吗？"
            @confirm="handleCancel(row)"
          >
            <template #reference>
              <el-button type="primary" link>
                作废
              </el-button>
            </template>
          </el-popconfirm>
          <el-button v-else type="info" link disabled>
            作废
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <el-dialog v-model="bindDialog.show" append-to-body top="30vh" width="500px" destroy-on-close>
      <el-text tag="b" size="large">
        输入用户手机号进行绑定
      </el-text>
      <div style="margin: 12px 0;">
        一个券只能绑定一个用户
      </div>
      <el-input v-model="bindDialog.mobile" maxlength="11" placeholder="请输入用户手机号" />

      <template #footer>
        <el-button @click="bindDialog.show = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleBind">
          确定
        </el-button>
      </template>
    </el-dialog>

    <el-dialog v-model="batchBindDialog.show" title="批量绑定用户" append-to-body align-center width="600px" destroy-on-close>
      <el-form label-width="140">
        <el-form-item label="每个用户绑定张数">
          <el-input-number
            v-model="batchBindDialog.num" :min="1" :max="9999" :step="1" :precision="0"
            controls-position="right" placeholder="请输入" style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="绑定方式">
          <el-radio-group v-model="batchBindDialog.type">
            <el-radio :value="0">
              手机号绑定
            </el-radio>
            <el-radio :value="1">
              账号绑定
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="batchBindDialog.type === 0" label="输入手机号">
          <el-input
            v-model="batchBindDialog.mobiles"
            type="textarea"
            rows="4"
            placeholder="请输入用户手机号，多个手机号换行输入"
          />
        </el-form-item>
        <el-form-item v-if="batchBindDialog.type === 1" label="选择用户（人）">
          <div>
            <el-button type="primary" @click="selectUserShow = true">
              指定用户
            </el-button>
            <div v-if="batchBindDialog.selectGroups.length" style="display: flex;margin-top: 8px;">
              <div>已选：</div>
              <div v-for="(item, index) in batchBindDialog.selectGroups" :key="index" style="margin-bottom: 8px;margin-left: 8px;">
                <el-tag
                  closable
                  @close="batchBindDialog.selectGroups.splice(index, 1)"
                >
                  {{ item.compName }}（{{ item.userIds.length }}）
                </el-tag>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="batchBindDialog.show = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleBatchBind">
          确定
        </el-button>
      </template>

      <selectCompanyUserComp
        v-if="selectUserShow"
        :model-value="selectUserShow"
        @submit="handleSelectUser"
        @close="selectUserShow = false"
      />
    </el-dialog>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
