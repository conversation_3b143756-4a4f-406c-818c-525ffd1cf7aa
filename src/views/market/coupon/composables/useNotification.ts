/**
 * 通知和反馈系统 Composable
 * 提供统一的消息提示、确认对话框、进度提示等功能
 */

import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading'

interface NotificationOptions {
  title?: string
  message?: string
  type?: 'success' | 'warning' | 'info' | 'error'
  duration?: number
  showClose?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

interface ConfirmOptions {
  title?: string
  message?: string
  type?: 'warning' | 'info' | 'success' | 'error'
  confirmButtonText?: string
  cancelButtonText?: string
  showCancelButton?: boolean
}

interface LoadingOptions {
  text?: string
  background?: string
  target?: string | HTMLElement
}

export function useNotification() {
  let loadingInstance: LoadingInstance | null = null

  // 成功消息
  const showSuccess = (message: string, duration = 3000) => {
    ElMessage({
      message,
      type: 'success',
      duration,
      showClose: true,
    })
  }

  // 错误消息
  const showError = (message: string, duration = 5000) => {
    ElMessage({
      message,
      type: 'error',
      duration,
      showClose: true,
    })
  }

  // 警告消息
  const showWarning = (message: string, duration = 4000) => {
    ElMessage({
      message,
      type: 'warning',
      duration,
      showClose: true,
    })
  }

  // 信息消息
  const showInfo = (message: string, duration = 3000) => {
    ElMessage({
      message,
      type: 'info',
      duration,
      showClose: true,
    })
  }

  // 通知
  const showNotification = (options: NotificationOptions) => {
    ElNotification({
      title: options.title || '通知',
      message: options.message || '',
      type: options.type || 'info',
      duration: options.duration || 4500,
      showClose: options.showClose !== false,
      position: options.position || 'top-right',
    })
  }

  // 成功通知
  const notifySuccess = (title: string, message?: string) => {
    showNotification({
      title,
      message,
      type: 'success',
    })
  }

  // 错误通知
  const notifyError = (title: string, message?: string) => {
    showNotification({
      title,
      message,
      type: 'error',
      duration: 6000,
    })
  }

  // 警告通知
  const notifyWarning = (title: string, message?: string) => {
    showNotification({
      title,
      message,
      type: 'warning',
    })
  }

  // 确认对话框
  const confirm = async (options: ConfirmOptions | string): Promise<boolean> => {
    try {
      const opts = typeof options === 'string' 
        ? { message: options }
        : options

      await ElMessageBox.confirm(
        opts.message || '确认执行此操作？',
        opts.title || '提示',
        {
          confirmButtonText: opts.confirmButtonText || '确定',
          cancelButtonText: opts.cancelButtonText || '取消',
          type: opts.type || 'warning',
          showCancelButton: opts.showCancelButton !== false,
        }
      )
      return true
    } catch {
      return false
    }
  }

  // 删除确认
  const confirmDelete = async (itemName?: string): Promise<boolean> => {
    const message = itemName 
      ? `确认删除"${itemName}"吗？此操作不可恢复。`
      : '确认删除选中项吗？此操作不可恢复。'
    
    return confirm({
      title: '删除确认',
      message,
      type: 'warning',
      confirmButtonText: '删除',
      cancelButtonText: '取消',
    })
  }

  // 批量删除确认
  const confirmBatchDelete = async (count: number): Promise<boolean> => {
    return confirm({
      title: '批量删除确认',
      message: `确认删除选中的 ${count} 项吗？此操作不可恢复。`,
      type: 'warning',
      confirmButtonText: '删除',
      cancelButtonText: '取消',
    })
  }

  // 显示加载
  const showLoading = (options: LoadingOptions = {}) => {
    loadingInstance = ElLoading.service({
      text: options.text || '加载中...',
      background: options.background || 'rgba(0, 0, 0, 0.7)',
      target: options.target,
    })
    return loadingInstance
  }

  // 隐藏加载
  const hideLoading = () => {
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }

  // 操作反馈包装器
  const withLoading = async <T>(
    operation: () => Promise<T>,
    loadingText = '处理中...'
  ): Promise<T> => {
    showLoading({ text: loadingText })
    try {
      const result = await operation()
      return result
    } finally {
      hideLoading()
    }
  }

  // 操作成功反馈
  const handleSuccess = (message: string, callback?: () => void) => {
    showSuccess(message)
    if (callback) {
      setTimeout(callback, 1000)
    }
  }

  // 操作失败反馈
  const handleError = (error: any, defaultMessage = '操作失败') => {
    const message = error?.message || error?.msg || defaultMessage
    showError(message)
    console.error('操作失败:', error)
  }

  // API调用包装器
  const apiCall = async <T>(
    operation: () => Promise<T>,
    options: {
      loadingText?: string
      successMessage?: string
      errorMessage?: string
      showSuccess?: boolean
      onSuccess?: (result: T) => void
      onError?: (error: any) => void
    } = {}
  ): Promise<T | null> => {
    try {
      if (options.loadingText) {
        showLoading({ text: options.loadingText })
      }

      const result = await operation()

      if (options.showSuccess !== false && options.successMessage) {
        showSuccess(options.successMessage)
      }

      if (options.onSuccess) {
        options.onSuccess(result)
      }

      return result
    } catch (error) {
      const errorMessage = options.errorMessage || '操作失败'
      handleError(error, errorMessage)
      
      if (options.onError) {
        options.onError(error)
      }

      return null
    } finally {
      if (options.loadingText) {
        hideLoading()
      }
    }
  }

  return {
    // 基础消息
    showSuccess,
    showError,
    showWarning,
    showInfo,
    
    // 通知
    showNotification,
    notifySuccess,
    notifyError,
    notifyWarning,
    
    // 确认对话框
    confirm,
    confirmDelete,
    confirmBatchDelete,
    
    // 加载状态
    showLoading,
    hideLoading,
    withLoading,
    
    // 操作反馈
    handleSuccess,
    handleError,
    apiCall,
  }
}
