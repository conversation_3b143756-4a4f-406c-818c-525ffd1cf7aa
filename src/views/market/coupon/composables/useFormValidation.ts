/**
 * 表单验证增强 Composable
 * 提供实时验证、错误提示、表单状态管理等功能
 */

import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any, callback: any) => void
  min?: number
  max?: number
  pattern?: RegExp
}

interface FieldConfig {
  [key: string]: ValidationRule[]
}

export function useFormValidation(initialData: Record<string, any> = {}) {
  const formRef = ref<FormInstance>()
  const formData = reactive({ ...initialData })
  const errors = reactive<Record<string, string>>({})
  const touched = reactive<Record<string, boolean>>({})
  const validating = ref(false)

  // 基础验证规则
  const baseRules: Record<string, ValidationRule[]> = {
    // 优惠券名称验证
    name: [
      { required: true, message: '请输入优惠券名称', trigger: 'blur' },
      { min: 2, max: 50, message: '优惠券名称长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    
    // 适用类型验证
    useOn: [
      { required: true, message: '请选择适用类型', trigger: 'change' },
    ],
    
    // 优惠券类型验证
    couponType: [
      { required: true, message: '请选择优惠券类型', trigger: 'change' },
    ],
    
    // 优惠金额验证
    discountAmount: [
      { required: true, message: '请输入优惠金额', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value <= 0) {
            callback(new Error('优惠金额必须大于0'))
          } else if (value > 10000) {
            callback(new Error('优惠金额不能超过10000元'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      },
    ],
    
    // 最低消费验证
    minAmount: [
      {
        validator: (rule, value, callback) => {
          if (value && value < 0) {
            callback(new Error('最低消费金额不能为负数'))
          } else if (value && formData.discountAmount && value <= formData.discountAmount) {
            callback(new Error('最低消费金额应大于优惠金额'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      },
    ],
    
    // 生成数量验证
    generateNum: [
      {
        validator: (rule, value, callback) => {
          if (formData.generateType === 1) {
            if (!value) {
              callback(new Error('请输入生成数量'))
            } else if (value <= 0) {
              callback(new Error('生成数量必须大于0'))
            } else if (value > 100000) {
              callback(new Error('生成数量不能超过100000'))
            } else {
              callback()
            }
          } else {
            callback()
          }
        },
        trigger: 'blur'
      },
    ],
    
    // 有效期验证
    validDateRange: [
      { required: true, message: '请选择有效期', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && value.length === 2) {
            const [start, end] = value
            const startDate = new Date(start)
            const endDate = new Date(end)
            const now = new Date()
            
            if (startDate < now) {
              callback(new Error('开始时间不能早于当前时间'))
            } else if (endDate <= startDate) {
              callback(new Error('结束时间必须晚于开始时间'))
            } else {
              const diffDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
              if (diffDays > 365) {
                callback(new Error('有效期不能超过365天'))
              } else {
                callback()
              }
            }
          } else {
            callback()
          }
        },
        trigger: 'change'
      },
    ],
  }

  // 动态规则
  const dynamicRules = computed<FormRules>(() => {
    const rules: FormRules = { ...baseRules }
    
    // 根据优惠券类型动态添加规则
    if (formData.couponType === 1) { // 满减券
      rules.minAmount = [
        { required: true, message: '请输入最低消费金额', trigger: 'blur' },
        ...baseRules.minAmount || []
      ]
    }
    
    return rules
  })

  // 表单是否有效
  const isValid = computed(() => {
    return Object.keys(errors).length === 0
  })

  // 表单是否已修改
  const isDirty = computed(() => {
    return Object.keys(touched).some(key => touched[key])
  })

  // 实时验证单个字段
  const validateField = async (field: string) => {
    if (!formRef.value) return false
    
    try {
      await formRef.value.validateField(field)
      delete errors[field]
      return true
    } catch (error) {
      errors[field] = error.message || '验证失败'
      return false
    }
  }

  // 验证整个表单
  const validateForm = async () => {
    if (!formRef.value) return false
    
    try {
      validating.value = true
      await formRef.value.validate()
      Object.keys(errors).forEach(key => delete errors[key])
      return true
    } catch (error) {
      console.error('表单验证失败:', error)
      return false
    } finally {
      validating.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    Object.keys(errors).forEach(key => delete errors[key])
    Object.keys(touched).forEach(key => delete touched[key])
  }

  // 清除验证
  const clearValidation = (fields?: string | string[]) => {
    if (formRef.value) {
      formRef.value.clearValidate(fields)
    }
    if (fields) {
      const fieldsArray = Array.isArray(fields) ? fields : [fields]
      fieldsArray.forEach(field => delete errors[field])
    } else {
      Object.keys(errors).forEach(key => delete errors[key])
    }
  }

  // 设置字段错误
  const setFieldError = (field: string, message: string) => {
    errors[field] = message
  }

  // 标记字段为已触摸
  const touchField = (field: string) => {
    touched[field] = true
  }

  // 监听表单数据变化，进行实时验证
  watch(
    formData,
    (newData, oldData) => {
      Object.keys(newData).forEach(key => {
        if (newData[key] !== oldData?.[key] && touched[key]) {
          // 延迟验证，避免输入时频繁验证
          setTimeout(() => {
            validateField(key)
          }, 300)
        }
      })
    },
    { deep: true }
  )

  return {
    formRef,
    formData,
    errors,
    touched,
    validating,
    dynamicRules,
    isValid,
    isDirty,
    validateField,
    validateForm,
    resetForm,
    clearValidation,
    setFieldError,
    touchField,
  }
}
