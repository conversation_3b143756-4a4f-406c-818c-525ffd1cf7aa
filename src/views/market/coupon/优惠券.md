# 文档

## 【后台】优惠券管理


### 优惠券列表
接口权限：/market/coupon/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/list


描述：优惠券列表
接口权限：/market/coupon/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| id | string | 否 | - | 优惠券Id |  |
| name | string | 否 | - | 优惠券名称 |  |
| useOn | int32 | 否 | - | 适用商品：电影票0，卖品1 | 0 |
| couponType | int32 | 否 | - | 券类型：满减0，减至1，通兑2，折扣3，多对一4 | 0 |
| cinemaScope | int32 | 否 | - | 影院范围：全部0，指定1，排除2  （影票） | 0 |
| cinemaId | int64 | 否 | - | 适用影院 | 0 |
| startTime | string | 否 | - | 有效日期 开始时间 |  |
| endTime | string | 否 | - | 有效日期 结束时间 |  |
| useStatus | int32 | 否 | - | 使用状态，0关闭，1开启 | 0 |
| num | int32 | 否 | - | 数量，按需生成0，指定数量生成>0 | 0 |
| generateType | int32 | 否 | - | 生成方式：0按需生成，1指定数量 | 0 |
| channelId | int64 | 否 | - | 渠道id | 0 |
| productId | int64 | 否 | - | 抖音商场券id | 0 |
| channelCode | string | 否 | - | 渠道编码 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "id": "",
    "name": "",
    "useOn": 0,
    "couponType": 0,
    "cinemaScope": 0,
    "cinemaId": 0,
    "startTime": "",
    "endTime": "",
    "useStatus": 0,
    "num": 0,
    "generateType": 0,
    "channelId": 0,
    "productId": 0,
    "channelCode": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | MongoDB自动生成的ID |  |
|   └ name | string | 否 | - | 优惠券名称 |  |
|   └ useOn | int32 | 否 | - | 适用商品：影票0，卖品1，演出2，展览3、电商 | 0 |
|   └ reduction | int32 | 否 | - | 服务费减免：减免0，不减免1 | 0 |
|   └ userServiceFee | int64 | 否 | - | 用户服务费(单位：分) | 0 |
|   └ note | string | 否 | - | 备注信息 |  |
|   └ remark | string | 否 | - | 备注说明 |  |
|   └ couponType | int32 | 否 | - | 优惠券类型: 满减0，减至1，通兑2，折扣3，多对一4 | 0 |
|   └ priceRule | object | 否 |  | 价格规则 |  |
|     └ priceTiers | array | 否 |  | 优惠规则 |  |
|       └ priceOrigin | int64 | 否 | - | 原始价格(单位：分) | 0 |
|       └ priceReduce | int64 | 否 | - | 减免金额(单位：分) | 0 |
|       └ priceDiff | int64 | 否 | - | 价格差值(单位：分) | 0 |
|     └ matchRule | int32 | 否 | - | 匹配规则 0 优惠补差 1 最低票价 | 0 |
|   └ channelRule | object | 否 |  | 适用对象 |  |
|     └ channelScope | int32 | 否 | - | 渠道范围<br>1-指定渠道 2-全部渠道 | 0 |
|     └ channels | array | 否 |  | 适用渠道列表 |  |
|       └ channelId | string | 否 | - | 渠道ID |  |
|       └ channelName | string | 否 | - | 渠道名称 |  |
|   └ generateRule | object | 否 |  | 生成规则 |  |
|     └ generateType | int32 | 否 | - | 生成方式：0、按需生成，1指定数量 | 0 |
|     └ num | int32 | 否 | - | 生成数量(当generateType=1时有效) | 0 |
|   └ settleRule | object | 否 |  | 结算规则(影院结算) |  |
|     └ method | int32 | 否 | - | 结算方式 0:定价规则 1:影院团购价 2.自定义结算 不能为空 | 0 |
|     └ priced | object | 否 |  | 自定义结算：settleMethod值为2，必传 |  |
|       └ type | int32 | 否 | - | 计算方式0-固定价格 1-最低票价 | 0 |
|       └ money | int32 | 否 | - | 定价金额(单位：分) | 0 |
|   └ periodRule | object | 否 |  | 时间有效规则 |  |
|     └ validScope | int32 | 否 | - | 有效范围：0 时间范围内有效，1 限时有效，绑定后 N天可用 | 0 |
|     └ overdueDay | int64 | 否 | - | 过期天数(从领取开始计算) | 0 |
|     └ startTime | int64 | 否 | - | 使用开始时间 | 0 |
|     └ endTime | int64 | 否 | - | 使用结束时间 | 0 |
|   └ createTime | int64 | 否 | - | 创建时间 | 0 |
|   └ updatedTime | int64 | 否 | - | 更新时间 | 0 |
|   └ bindCount | int32 | 否 | - | 券已绑定数 | 0 |
|   └ usedCount | int32 | 否 | - | 券已使用数 | 0 |
|   └ useStatus | int32 | 否 | - | 使用状态，0关闭，1开启，2 草稿 | 0 |
|   └ generateStatus | int32 | 否 | - | 生成状态，0未生成，1已生成 | 0 |
|   └ deleted | boolean | 否 | - | 是否删除 | true |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | MongoDB自动生成的ID |  |
|   └ name | string | 否 | - | 优惠券名称 |  |
|   └ useOn | int32 | 否 | - | 适用商品：影票0，卖品1，演出2，展览3、电商 | 0 |
|   └ reduction | int32 | 否 | - | 服务费减免：减免0，不减免1 | 0 |
|   └ userServiceFee | int64 | 否 | - | 用户服务费(单位：分) | 0 |
|   └ note | string | 否 | - | 备注信息 |  |
|   └ remark | string | 否 | - | 备注说明 |  |
|   └ couponType | int32 | 否 | - | 优惠券类型: 满减0，减至1，通兑2，折扣3，多对一4 | 0 |
|   └ priceRule | object | 否 |  | 价格规则 |  |
|     └ priceTiers | array | 否 |  | 优惠规则 |  |
|       └ priceOrigin | int64 | 否 | - | 原始价格(单位：分) | 0 |
|       └ priceReduce | int64 | 否 | - | 减免金额(单位：分) | 0 |
|       └ priceDiff | int64 | 否 | - | 价格差值(单位：分) | 0 |
|     └ matchRule | int32 | 否 | - | 匹配规则 0 优惠补差 1 最低票价 | 0 |
|   └ channelRule | object | 否 |  | 适用对象 |  |
|     └ channelScope | int32 | 否 | - | 渠道范围<br>1-指定渠道 2-全部渠道 | 0 |
|     └ channels | array | 否 |  | 适用渠道列表 |  |
|       └ channelId | string | 否 | - | 渠道ID |  |
|       └ channelName | string | 否 | - | 渠道名称 |  |
|   └ generateRule | object | 否 |  | 生成规则 |  |
|     └ generateType | int32 | 否 | - | 生成方式：0、按需生成，1指定数量 | 0 |
|     └ num | int32 | 否 | - | 生成数量(当generateType=1时有效) | 0 |
|   └ settleRule | object | 否 |  | 结算规则(影院结算) |  |
|     └ method | int32 | 否 | - | 结算方式 0:定价规则 1:影院团购价 2.自定义结算 不能为空 | 0 |
|     └ priced | object | 否 |  | 自定义结算：settleMethod值为2，必传 |  |
|       └ type | int32 | 否 | - | 计算方式0-固定价格 1-最低票价 | 0 |
|       └ money | int32 | 否 | - | 定价金额(单位：分) | 0 |
|   └ periodRule | object | 否 |  | 时间有效规则 |  |
|     └ validScope | int32 | 否 | - | 有效范围：0 时间范围内有效，1 限时有效，绑定后 N天可用 | 0 |
|     └ overdueDay | int64 | 否 | - | 过期天数(从领取开始计算) | 0 |
|     └ startTime | int64 | 否 | - | 使用开始时间 | 0 |
|     └ endTime | int64 | 否 | - | 使用结束时间 | 0 |
|   └ createTime | int64 | 否 | - | 创建时间 | 0 |
|   └ updatedTime | int64 | 否 | - | 更新时间 | 0 |
|   └ bindCount | int32 | 否 | - | 券已绑定数 | 0 |
|   └ usedCount | int32 | 否 | - | 券已使用数 | 0 |
|   └ useStatus | int32 | 否 | - | 使用状态，0关闭，1开启，2 草稿 | 0 |
|   └ generateStatus | int32 | 否 | - | 生成状态，0未生成，1已生成 | 0 |
|   └ deleted | boolean | 否 | - | 是否删除 | true |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "useOn": 0,
            "reduction": 0,
            "userServiceFee": 0,
            "note": "",
            "remark": "",
            "couponType": 0,
            "priceRule": {
                "priceTiers": [
                    {
                        "priceOrigin": 0,
                        "priceReduce": 0,
                        "priceDiff": 0
                    }
                ],
                "matchRule": 0
            },
            "channelRule": {
                "channelScope": 0,
                "channels": [
                    {
                        "channelId": "",
                        "channelName": ""
                    }
                ]
            },
            "generateRule": {
                "generateType": 0,
                "num": 0
            },
            "settleRule": {
                "method": 0,
                "priced": {
                    "type": 0,
                    "money": 0
                }
            },
            "periodRule": {
                "validScope": 0,
                "overdueDay": 0,
                "startTime": 0,
                "endTime": 0
            },
            "createTime": 0,
            "updatedTime": 0,
            "bindCount": 0,
            "usedCount": 0,
            "useStatus": 0,
            "generateStatus": 0,
            "deleted": true
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "useOn": 0,
            "reduction": 0,
            "userServiceFee": 0,
            "note": "",
            "remark": "",
            "couponType": 0,
            "priceRule": {
                "priceTiers": [
                    {
                        "priceOrigin": 0,
                        "priceReduce": 0,
                        "priceDiff": 0
                    }
                ],
                "matchRule": 0
            },
            "channelRule": {
                "channelScope": 0,
                "channels": [
                    {
                        "channelId": "",
                        "channelName": ""
                    }
                ]
            },
            "generateRule": {
                "generateType": 0,
                "num": 0
            },
            "settleRule": {
                "method": 0,
                "priced": {
                    "type": 0,
                    "money": 0
                }
            },
            "periodRule": {
                "validScope": 0,
                "overdueDay": 0,
                "startTime": 0,
                "endTime": 0
            },
            "createTime": 0,
            "updatedTime": 0,
            "bindCount": 0,
            "usedCount": 0,
            "useStatus": 0,
            "generateStatus": 0,
            "deleted": true
        }
    ]
}
```

#### 错误码

无

### 查看优券码列表
接口权限：/market/coupon/code/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/listCouponCode


描述：查看优券码列表
接口权限：/market/coupon/code/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| id | string | 否 | - | 券码Id |  |
| couponId | string | 否 | - | 关联的优惠券ID,必填 |  |
| code | string | 否 | - | 优惠码(如：A003-XXXX-YYYY) |  |
| status | int32 | 否 | - | 券码状态，0未绑定，1已绑定，2成功使用，3使用失败，4未绑定过期，5已绑定过期，6作废 | 0 |
| orderId | string | 否 | - | 关联的订单ID |  |
| mobile | string | 否 | - | 绑定用户手机号 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "id": "",
    "couponId": "",
    "code": "",
    "status": 0,
    "orderId": "",
    "mobile": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ couponId | string | 否 | - | 优惠券ID |  |
|   └ name | string | 否 | - | 优惠券名称 |  |
|   └ code | string | 否 | - | 优惠券码 |  |
|   └ userId | string | 否 | - | 绑定用户ID |  |
|   └ mobile | string | 否 | - | 绑定用户手机号 |  |
|   └ orderNo | string | 否 | - | 订单ID |  |
|   └ generateTime | int64 | 否 | - | 生成时间 | 0 |
|   └ bindTime | int64 | 否 | - | 绑定时间 | 0 |
|   └ startUseTime | int64 | 否 | - | 开始使用时间 | 0 |
|   └ endUseTime | int64 | 否 | - | 截止使用时间 | 0 |
|   └ useTime | int64 | 否 | - | 使用时间 | 0 |
|   └ status | int32 | 否 | - | 券码状态，0未绑定，1已绑定，2成功使用，3使用失败，4未绑定过期，5已绑定过期，6作废 | 0 |
|   └ appId | string | 否 | - | 小程序appid |  |
|   └ source | string | 否 | - | 来源 |  |
|   └ useOn | int32 | 否 | - | 适用商品：电影票0，卖品1，演出2 | 0 |
|   └ delFlag | boolean | 否 | - | 是否删除 0 否  1 删除 | true |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ couponId | string | 否 | - | 优惠券ID |  |
|   └ name | string | 否 | - | 优惠券名称 |  |
|   └ code | string | 否 | - | 优惠券码 |  |
|   └ userId | string | 否 | - | 绑定用户ID |  |
|   └ mobile | string | 否 | - | 绑定用户手机号 |  |
|   └ orderNo | string | 否 | - | 订单ID |  |
|   └ generateTime | int64 | 否 | - | 生成时间 | 0 |
|   └ bindTime | int64 | 否 | - | 绑定时间 | 0 |
|   └ startUseTime | int64 | 否 | - | 开始使用时间 | 0 |
|   └ endUseTime | int64 | 否 | - | 截止使用时间 | 0 |
|   └ useTime | int64 | 否 | - | 使用时间 | 0 |
|   └ status | int32 | 否 | - | 券码状态，0未绑定，1已绑定，2成功使用，3使用失败，4未绑定过期，5已绑定过期，6作废 | 0 |
|   └ appId | string | 否 | - | 小程序appid |  |
|   └ source | string | 否 | - | 来源 |  |
|   └ useOn | int32 | 否 | - | 适用商品：电影票0，卖品1，演出2 | 0 |
|   └ delFlag | boolean | 否 | - | 是否删除 0 否  1 删除 | true |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "couponId": "",
            "name": "",
            "code": "",
            "userId": "",
            "mobile": "",
            "orderNo": "",
            "generateTime": 0,
            "bindTime": 0,
            "startUseTime": 0,
            "endUseTime": 0,
            "useTime": 0,
            "status": 0,
            "appId": "",
            "source": "",
            "useOn": 0,
            "delFlag": true
        }
    ],
    "result": [
        {
            "id": "",
            "couponId": "",
            "name": "",
            "code": "",
            "userId": "",
            "mobile": "",
            "orderNo": "",
            "generateTime": 0,
            "bindTime": 0,
            "startUseTime": 0,
            "endUseTime": 0,
            "useTime": 0,
            "status": 0,
            "appId": "",
            "source": "",
            "useOn": 0,
            "delFlag": true
        }
    ]
}
```

#### 错误码

无

### 创建优惠券
接口权限：/market/coupon/add

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/add


描述：创建优惠券
接口权限：/market/coupon/add

ContentType：`application/json`

#### 请求头

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| adminToken | 是 |  | wec76PvDb8fjXUrnRQon9e77r1BuoGsMyRiVsghCBZ4SjbkvojTNcWWMLYuSDgco9InJMOhy74+JcUhVLIp+KEjaz9840jtRV6pl6KqYUlKFA0Eldp1AlRKTywKhBBm3 |

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| name | string | 否 | - | 优惠券名称 |  |
| useOn | int32 | 否 | - | 适用商品：影票0，卖品1，演出2，展览3 | 0 |
| couponType | int32 | 否 | - | 优惠券类型 满减0，减至1，通兑2，折扣3，多对一4 | 0 |
| reduction | int32 | 否 | - | 服务费减免：减免0，不减免1 | 0 |
| userServiceFee | int64 | 否 | - | 用户服务费(单位：分) | 0 |
| priceRule | object | 否 |  | 价格规则 |  |
|   └ priceTiers | array | 否 |  | 优惠规则 |  |
|     └ priceOrigin | int64 | 否 | - | 原始价格(单位：分) | 0 |
|     └ priceReduce | int64 | 否 | - | 减免金额(单位：分) | 0 |
|     └ priceDiff | int64 | 否 | - | 价格差值(单位：分) | 0 |
|   └ matchRule | int32 | 否 | - | 匹配规则 0 优惠补差 1 最低票价 | 0 |
| channelRule | object | 否 |  | 适用对象 |  |
|   └ channelScope | int32 | 否 | - | 渠道范围<br>1-指定渠道 2-全部渠道 | 0 |
|   └ channels | array | 否 |  | 适用渠道列表 |  |
|     └ channelId | string | 否 | - | 渠道ID |  |
|     └ channelName | string | 否 | - | 渠道名称 |  |
| generateRule | object | 否 |  | 生成规则 |  |
|   └ generateType | int32 | 否 | - | 生成方式：0、按需生成，1指定数量 | 0 |
|   └ num | int32 | 否 | - | 生成数量(当generateType=1时有效) | 0 |
| settleRule | object | 否 |  | 结算规则(影院结算) |  |
|   └ method | int32 | 否 | - | 结算方式 0:定价规则 1:影院团购价 2.自定义结算 不能为空 | 0 |
|   └ priced | object | 否 |  | 自定义结算：settleMethod值为2，必传 |  |
|     └ type | int32 | 否 | - | 计算方式0-固定价格 1-最低票价 | 0 |
|     └ money | int32 | 否 | - | 定价金额(单位：分) | 0 |
| periodRule | object | 否 |  | 时间有效规则 |  |
|   └ validScope | int32 | 否 | - | 有效范围：0 时间范围内有效，1 限时有效，绑定后 N天可用 | 0 |
|   └ overdueDay | int64 | 否 | - | 过期天数(从领取开始计算) | 0 |
|   └ startTime | int64 | 否 | - | 使用开始时间 | 0 |
|   └ endTime | int64 | 否 | - | 使用结束时间 | 0 |
| note | string | 否 | - | 备注信息 |  |
| valuable | int32 | 否 | - | 优惠券价值 | 0 |
| remark | string | 否 | - | 备注说明 |  |

#### 请求示例

```
{
    "name": "",
    "useOn": 0,
    "couponType": 0,
    "reduction": 0,
    "userServiceFee": 0,
    "priceRule": {
        "priceTiers": [
            {
                "priceOrigin": 0,
                "priceReduce": 0,
                "priceDiff": 0
            }
        ],
        "matchRule": 0
    },
    "channelRule": {
        "channelScope": 0,
        "channels": [
            {
                "channelId": "",
                "channelName": ""
            }
        ]
    },
    "generateRule": {
        "generateType": 0,
        "num": 0
    },
    "settleRule": {
        "method": 0,
        "priced": {
            "type": 0,
            "money": 0
        }
    },
    "periodRule": {
        "validScope": 0,
        "overdueDay": 0,
        "startTime": 0,
        "endTime": 0
    },
    "note": "",
    "valuable": 0,
    "remark": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 编辑优惠券
接口权限：/market/coupon/save

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/save


描述：编辑优惠券
接口权限：/market/coupon/save

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 优惠券Id |  |
| name | string | 否 | - | 优惠券名称 |  |
| periodRule | object | 否 |  | 时间有效规则 |  |
|   └ validScope | int32 | 否 | - | 有效范围：0 时间范围内有效，1 限时有效，绑定后 N天可用 | 0 |
|   └ overdueDay | int64 | 否 | - | 过期天数(从领取开始计算) | 0 |
|   └ startTime | int64 | 否 | - | 使用开始时间 | 0 |
|   └ endTime | int64 | 否 | - | 使用结束时间 | 0 |
| note | string | 否 | - | 备注信息 |  |
| valuable | int32 | 否 | - | 优惠券价值 | 0 |
| remark | string | 否 | - | 备注说明 |  |
| channelRule | object | 否 |  | 渠道规则 |  |
|   └ channelScope | int32 | 否 | - | 渠道范围<br>1-指定渠道 2-全部渠道 | 0 |
|   └ channels | array | 否 |  | 适用渠道列表 |  |
|     └ channelId | string | 否 | - | 渠道ID |  |
|     └ channelName | string | 否 | - | 渠道名称 |  |
| generateRule | object | 否 |  | 生成规则 |  |
|   └ generateType | int32 | 否 | - | 生成方式：0、按需生成，1指定数量 | 0 |
|   └ num | int32 | 否 | - | 生成数量(当generateType=1时有效) | 0 |
| settleRule | object | 否 |  | 结算规则(影院结算) |  |
|   └ method | int32 | 否 | - | 结算方式 0:定价规则 1:影院团购价 2.自定义结算 不能为空 | 0 |
|   └ priced | object | 否 |  | 自定义结算：settleMethod值为2，必传 |  |
|     └ type | int32 | 否 | - | 计算方式0-固定价格 1-最低票价 | 0 |
|     └ money | int32 | 否 | - | 定价金额(单位：分) | 0 |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "periodRule": {
        "validScope": 0,
        "overdueDay": 0,
        "startTime": 0,
        "endTime": 0
    },
    "note": "",
    "valuable": 0,
    "remark": "",
    "channelRule": {
        "channelScope": 0,
        "channels": [
            {
                "channelId": "",
                "channelName": ""
            }
        ]
    },
    "generateRule": {
        "generateType": 0,
        "num": 0
    },
    "settleRule": {
        "method": 0,
        "priced": {
            "type": 0,
            "money": 0
        }
    }
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 优惠券 状态变更 开启or关闭
接口权限：/market/coupon/status

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/status


描述：优惠券 状态变更 开启or关闭
接口权限：/market/coupon/status

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 优惠券ID不能为空 |  |
| useStatus | int32 | 是 | - | 使用状态，0关闭，1开启 不能为空 | 0 |

#### 请求示例

```
{
    "id": "",
    "useStatus": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 绑定优惠券
接口权限：/market/coupon/bind

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/bind


描述：绑定优惠券
接口权限：/market/coupon/bind

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 优惠券ID不能为空 |  |
| userIds | array | 否 | - | 绑定批量用户ID | , |
| mobiles | array | 是 | - | 绑定用户手机号 | , |
| num | int32 | 否 | - | 数量 | 0 |
| bindType | int32 | 否 | - | 绑定类型 0 手机号批量绑定   1 用户账号批量绑定  2 手机号单个绑定 | 0 |
| codeId | string | 是 | - | 优惠券码Id |  |

#### 请求示例

```
{
    "id": "",
    "userIds": [
        0,
        0
    ],
    "mobiles": [
        0,
        0
    ],
    "num": 0,
    "bindType": 0,
    "codeId": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 创建优惠券限制规则
接口权限：/market/coupon/restriction/add

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/createRestriction


描述：创建优惠券限制规则
接口权限：/market/coupon/restriction/add

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| couponId | string | 否 | - | 优惠券 ID |  |
| ticketRestriction | object | 否 |  | 影票优惠券 限制规则 |  |
|   └ couponId | string | 否 | - | 优惠券 ID |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ cinemaRestriction | object | 否 |  | 影院限制 |  |
|     └ cinemaScope | int32 | 否 | - | 影院范围：全部0，指定1，排除2 | 0 |
|     └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|       └ id | int32 | 否 | - |  | 0 |
|       └ cinemaId | string | 否 | - | 影院ID |  |
|       └ cinemaName | string | 否 | - | 影院名称 |  |
|       └ halls | array | 否 |  | 影厅 |  |
|         └ hallId | string | 否 | - | 影厅ID |  |
|         └ hallName | string | 否 | - | 影厅名称 |  |
|   └ filmRestriction | object | 否 |  | 影片限制 |  |
|     └ filmScope | int32 | 否 | - | 适用影片：全部影片0，指定影片1，最低价影片2 | 0 |
|     └ films | array | 否 |  | 优惠券适用影片 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ filmId | int64 | 否 | - | 影片ID | 0 |
|       └ filmCode | string | 否 | - | 影片国家编码 |  |
|       └ filmName | string | 否 | - | 影片名称 |  |
|   └ periodRestriction | object | 否 |  | 时间段限制 |  |
|     └ periodScope | int32 | 否 | - | 适用时段：全部时段0，指定时段1 | 0 |
|     └ periods | array | 否 |  | 优惠券适用时段 |  |
|       └ id | int64 | 否 | - | ID | 0 |
|       └ num | int32 | 否 | - | 时段序号 | 0 |
|       └ day | int32 | 否 | - | 星期几 | 0 |
|       └ start | int64 | 否 | - | 开始时间：2030（晚上8点半） | 0 |
|       └ end | int64 | 否 | - | 结束时间：2400（晚上12点） | 0 |
|   └ filmVersionRestriction | object | 否 |  | 电影版本限制 |  |
|     └ versionScope | int32 | 否 | - | 适用影片版本：全部影片版本0，指定影片版本1 | 0 |
|     └ versions | array | 否 |  | 优惠券适用影片版本 |  |
|       └ filmVersion | string | 否 | - | 影片版本 |  |
|       └ versionId | string | 否 | - | No comments found. |  |
| showRestriction | object | 否 |  | 演出优惠券 限制规则 |  |
|   └ showScope | int32 | 是 | - | 适用演出: 全部 0，指定 1，排除 2 | 0 |
|   └ shows | array | 否 |  | 优惠券适用影院对象 |  |
|     └ cinemaId | int64 | 否 | - | 演出场馆id | 0 |
|     └ cinemaName | string | 否 | - | 演出场馆 |  |
|     └ showName | string | 否 | - | 演出名称 |  |
|     └ showId | string | 否 | - | 演出Id |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ sessions | array | 否 |  | 场次 |  |
|       └ id | string | 否 | - | No comments found. |  |
|       └ showScheduleId | int32 | 是 | - | 演出场次ID | 0 |
|       └ startTime | int64 | 是 | - | 开始时间 | 0 |
|       └ endTime | int64 | 是 | - | 结束时间 | 0 |
| goodsRestriction | object | 否 |  | 卖品优惠券 限制规则 |  |
|   └ goodsScope | int32 | 是 | - | 适用卖品分类: 全部0，指定1，排除2 | 0 |
|   └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|     └ id | int32 | 否 | - |  | 0 |
|     └ cinemaId | string | 否 | - | 影院ID |  |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ halls | array | 否 |  | 影厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|   └ goodsTypeIds | array | 否 | - | 优惠券适用商品类型 | 0,0 |
|   └ goods | array | 否 |  | 优惠券适用指定商品 |  |
|     └ id | int64 | 是 | - |  | 0 |
|     └ cinemaId | int64 | 是 | - | 影院ID | 0 |
|     └ cinemaName | string | 是 | - | 影院名称 |  |
|     └ goodsId | string | 是 | - | 卖品ID |  |
|     └ goodsName | string | 是 | - | 卖品名称 |  |
| exhibitionRestriction | object | 否 |  | 展览优惠券 限制规则 |  |
|   └ exhibitScope | int32 | 是 | - | 展览范围：全部 0，指定 1，排除 2 | 0 |
|   └ exhibitHouses | array | 否 |  | 优惠券适用（场馆&展厅） |  |
|     └ halls | array | 否 |  | 展厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|     └ houseId | string | 否 | - | No comments found. |  |
|   └ exhibits | array | 否 |  | 优惠券适用（展品） |  |
|     └ exhibitId | int64 | 否 | - | 展览ID | 0 |
|     └ exhibitName | string | 否 | - | 展览名称 |  |

#### 请求示例

```
{
    "couponId": "",
    "ticketRestriction": {
        "couponId": "",
        "id": "",
        "cinemaRestriction": {
            "cinemaScope": 0,
            "cinemas": [
                {
                    "id": 0,
                    "cinemaId": "",
                    "cinemaName": "",
                    "halls": [
                        {
                            "hallId": "",
                            "hallName": ""
                        }
                    ]
                }
            ]
        },
        "filmRestriction": {
            "filmScope": 0,
            "films": [
                {
                    "id": 0,
                    "filmId": 0,
                    "filmCode": "",
                    "filmName": ""
                }
            ]
        },
        "periodRestriction": {
            "periodScope": 0,
            "periods": [
                {
                    "id": 0,
                    "num": 0,
                    "day": 0,
                    "start": 0,
                    "end": 0
                }
            ]
        },
        "filmVersionRestriction": {
            "versionScope": 0,
            "versions": [
                {
                    "filmVersion": "",
                    "versionId": ""
                }
            ]
        }
    },
    "showRestriction": {
        "showScope": 0,
        "shows": [
            {
                "cinemaId": 0,
                "cinemaName": "",
                "showName": "",
                "showId": "",
                "id": "",
                "sessions": [
                    {
                        "id": "",
                        "showScheduleId": 0,
                        "startTime": 0,
                        "endTime": 0
                    }
                ]
            }
        ]
    },
    "goodsRestriction": {
        "goodsScope": 0,
        "cinemas": [
            {
                "id": 0,
                "cinemaId": "",
                "cinemaName": "",
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ]
            }
        ],
        "goodsTypeIds": [
            0,
            0
        ],
        "goods": [
            {
                "id": 0,
                "cinemaId": 0,
                "cinemaName": "",
                "goodsId": "",
                "goodsName": ""
            }
        ]
    },
    "exhibitionRestriction": {
        "exhibitScope": 0,
        "exhibitHouses": [
            {
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ],
                "houseId": ""
            }
        ],
        "exhibits": [
            {
                "exhibitId": 0,
                "exhibitName": ""
            }
        ]
    }
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 编辑优惠券限制规则
接口权限：/market/coupon/restriction/save

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/updateRestriction


描述：编辑优惠券限制规则
接口权限：/market/coupon/restriction/save

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| restrictionId | string | 否 | - | 限制规则 ID |  |
| couponId | string | 否 | - | 优惠券 ID |  |
| ticketRestriction | object | 否 |  | 影票优惠券 限制规则 |  |
|   └ couponId | string | 否 | - | 优惠券 ID |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ cinemaRestriction | object | 否 |  | 影院限制 |  |
|     └ cinemaScope | int32 | 否 | - | 影院范围：全部0，指定1，排除2 | 0 |
|     └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|       └ id | int32 | 否 | - |  | 0 |
|       └ cinemaId | string | 否 | - | 影院ID |  |
|       └ cinemaName | string | 否 | - | 影院名称 |  |
|       └ halls | array | 否 |  | 影厅 |  |
|         └ hallId | string | 否 | - | 影厅ID |  |
|         └ hallName | string | 否 | - | 影厅名称 |  |
|   └ filmRestriction | object | 否 |  | 影片限制 |  |
|     └ filmScope | int32 | 否 | - | 适用影片：全部影片0，指定影片1，最低价影片2 | 0 |
|     └ films | array | 否 |  | 优惠券适用影片 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ filmId | int64 | 否 | - | 影片ID | 0 |
|       └ filmCode | string | 否 | - | 影片国家编码 |  |
|       └ filmName | string | 否 | - | 影片名称 |  |
|   └ periodRestriction | object | 否 |  | 时间段限制 |  |
|     └ periodScope | int32 | 否 | - | 适用时段：全部时段0，指定时段1 | 0 |
|     └ periods | array | 否 |  | 优惠券适用时段 |  |
|       └ id | int64 | 否 | - | ID | 0 |
|       └ num | int32 | 否 | - | 时段序号 | 0 |
|       └ day | int32 | 否 | - | 星期几 | 0 |
|       └ start | int64 | 否 | - | 开始时间：2030（晚上8点半） | 0 |
|       └ end | int64 | 否 | - | 结束时间：2400（晚上12点） | 0 |
|   └ filmVersionRestriction | object | 否 |  | 电影版本限制 |  |
|     └ versionScope | int32 | 否 | - | 适用影片版本：全部影片版本0，指定影片版本1 | 0 |
|     └ versions | array | 否 |  | 优惠券适用影片版本 |  |
|       └ filmVersion | string | 否 | - | 影片版本 |  |
|       └ versionId | string | 否 | - | No comments found. |  |
| showRestrictions | object | 否 |  | 演出优惠券 限制规则 |  |
|   └ showScope | int32 | 是 | - | 适用演出: 全部 0，指定 1，排除 2 | 0 |
|   └ shows | array | 否 |  | 优惠券适用影院对象 |  |
|     └ cinemaId | int64 | 否 | - | 演出场馆id | 0 |
|     └ cinemaName | string | 否 | - | 演出场馆 |  |
|     └ showName | string | 否 | - | 演出名称 |  |
|     └ showId | string | 否 | - | 演出Id |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ sessions | array | 否 |  | 场次 |  |
|       └ id | string | 否 | - | No comments found. |  |
|       └ showScheduleId | int32 | 是 | - | 演出场次ID | 0 |
|       └ startTime | int64 | 是 | - | 开始时间 | 0 |
|       └ endTime | int64 | 是 | - | 结束时间 | 0 |
| goodsRestrictions | object | 否 |  | 卖品优惠券 限制规则 |  |
|   └ goodsScope | int32 | 是 | - | 适用卖品分类: 全部0，指定1，排除2 | 0 |
|   └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|     └ id | int32 | 否 | - |  | 0 |
|     └ cinemaId | string | 否 | - | 影院ID |  |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ halls | array | 否 |  | 影厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|   └ goodsTypeIds | array | 否 | - | 优惠券适用商品类型 | 0,0 |
|   └ goods | array | 否 |  | 优惠券适用指定商品 |  |
|     └ id | int64 | 是 | - |  | 0 |
|     └ cinemaId | int64 | 是 | - | 影院ID | 0 |
|     └ cinemaName | string | 是 | - | 影院名称 |  |
|     └ goodsId | string | 是 | - | 卖品ID |  |
|     └ goodsName | string | 是 | - | 卖品名称 |  |
| exhibitionRestrictions | object | 否 |  | 展览优惠券 限制规则 |  |
|   └ exhibitScope | int32 | 是 | - | 展览范围：全部 0，指定 1，排除 2 | 0 |
|   └ exhibitHouses | array | 否 |  | 优惠券适用（场馆&展厅） |  |
|     └ halls | array | 否 |  | 展厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|     └ houseId | string | 否 | - | No comments found. |  |
|   └ exhibits | array | 否 |  | 优惠券适用（展品） |  |
|     └ exhibitId | int64 | 否 | - | 展览ID | 0 |
|     └ exhibitName | string | 否 | - | 展览名称 |  |

#### 请求示例

```
{
    "restrictionId": "",
    "couponId": "",
    "ticketRestriction": {
        "couponId": "",
        "id": "",
        "cinemaRestriction": {
            "cinemaScope": 0,
            "cinemas": [
                {
                    "id": 0,
                    "cinemaId": "",
                    "cinemaName": "",
                    "halls": [
                        {
                            "hallId": "",
                            "hallName": ""
                        }
                    ]
                }
            ]
        },
        "filmRestriction": {
            "filmScope": 0,
            "films": [
                {
                    "id": 0,
                    "filmId": 0,
                    "filmCode": "",
                    "filmName": ""
                }
            ]
        },
        "periodRestriction": {
            "periodScope": 0,
            "periods": [
                {
                    "id": 0,
                    "num": 0,
                    "day": 0,
                    "start": 0,
                    "end": 0
                }
            ]
        },
        "filmVersionRestriction": {
            "versionScope": 0,
            "versions": [
                {
                    "filmVersion": "",
                    "versionId": ""
                }
            ]
        }
    },
    "showRestrictions": {
        "showScope": 0,
        "shows": [
            {
                "cinemaId": 0,
                "cinemaName": "",
                "showName": "",
                "showId": "",
                "id": "",
                "sessions": [
                    {
                        "id": "",
                        "showScheduleId": 0,
                        "startTime": 0,
                        "endTime": 0
                    }
                ]
            }
        ]
    },
    "goodsRestrictions": {
        "goodsScope": 0,
        "cinemas": [
            {
                "id": 0,
                "cinemaId": "",
                "cinemaName": "",
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ]
            }
        ],
        "goodsTypeIds": [
            0,
            0
        ],
        "goods": [
            {
                "id": 0,
                "cinemaId": 0,
                "cinemaName": "",
                "goodsId": "",
                "goodsName": ""
            }
        ]
    },
    "exhibitionRestrictions": {
        "exhibitScope": 0,
        "exhibitHouses": [
            {
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ],
                "houseId": ""
            }
        ],
        "exhibits": [
            {
                "exhibitId": 0,
                "exhibitName": ""
            }
        ]
    }
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 获取优惠券限制规则详情
接口权限：/market/coupon/restriction/get

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/getRestriction


描述：获取优惠券限制规则详情
接口权限：/market/coupon/restriction/get

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| couponId | string | 否 | - | 优惠券 ID |  |
| useOn | int32 | 否 | - | 适用商品：影票0，卖品1，演出2，展览3 | 0 |

#### 请求示例

```
{
    "couponId": "",
    "useOn": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| couponId | string | 否 | - | 优惠券 ID |  |
| ticketRestriction | object | 否 |  | 影票优惠券 限制规则 |  |
|   └ couponId | string | 否 | - | 优惠券 ID |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ cinemaRestriction | object | 否 |  | 影院限制 |  |
|     └ cinemaScope | int32 | 否 | - | 影院范围：全部0，指定1，排除2 | 0 |
|     └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|       └ id | int32 | 否 | - |  | 0 |
|       └ cinemaId | string | 否 | - | 影院ID |  |
|       └ cinemaName | string | 否 | - | 影院名称 |  |
|       └ halls | array | 否 |  | 影厅 |  |
|         └ hallId | string | 否 | - | 影厅ID |  |
|         └ hallName | string | 否 | - | 影厅名称 |  |
|   └ filmRestriction | object | 否 |  | 影片限制 |  |
|     └ filmScope | int32 | 否 | - | 适用影片：全部影片0，指定影片1，最低价影片2 | 0 |
|     └ films | array | 否 |  | 优惠券适用影片 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ filmId | int64 | 否 | - | 影片ID | 0 |
|       └ filmCode | string | 否 | - | 影片国家编码 |  |
|       └ filmName | string | 否 | - | 影片名称 |  |
|   └ periodRestriction | object | 否 |  | 时间段限制 |  |
|     └ periodScope | int32 | 否 | - | 适用时段：全部时段0，指定时段1 | 0 |
|     └ periods | array | 否 |  | 优惠券适用时段 |  |
|       └ id | int64 | 否 | - | ID | 0 |
|       └ num | int32 | 否 | - | 时段序号 | 0 |
|       └ day | int32 | 否 | - | 星期几 | 0 |
|       └ start | int64 | 否 | - | 开始时间：2030（晚上8点半） | 0 |
|       └ end | int64 | 否 | - | 结束时间：2400（晚上12点） | 0 |
|   └ filmVersionRestriction | object | 否 |  | 电影版本限制 |  |
|     └ versionScope | int32 | 否 | - | 适用影片版本：全部影片版本0，指定影片版本1 | 0 |
|     └ versions | array | 否 |  | 优惠券适用影片版本 |  |
|       └ filmVersion | string | 否 | - | 影片版本 |  |
|       └ versionId | string | 否 | - | No comments found. |  |
| showRestriction | object | 否 |  | 演出优惠券 限制规则 |  |
|   └ showScope | int32 | 否 | - | 适用演出: 全部 0，指定 1，排除 2 | 0 |
|   └ shows | array | 否 |  | 优惠券适用影院对象 |  |
|     └ cinemaId | int64 | 否 | - | 演出场馆id | 0 |
|     └ cinemaName | string | 否 | - | 演出场馆 |  |
|     └ showName | string | 否 | - | 演出名称 |  |
|     └ showId | string | 否 | - | 演出Id |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ sessions | array | 否 |  | 场次 |  |
|       └ id | string | 否 | - | No comments found. |  |
|       └ showScheduleId | int32 | 否 | - | 演出场次ID | 0 |
|       └ startTime | int64 | 否 | - | 开始时间 | 0 |
|       └ endTime | int64 | 否 | - | 结束时间 | 0 |
| goodsRestriction | object | 否 |  | 卖品优惠券 限制规则 |  |
|   └ goodsScope | int32 | 否 | - | 适用卖品分类: 全部0，指定1，排除2 | 0 |
|   └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|     └ id | int32 | 否 | - |  | 0 |
|     └ cinemaId | string | 否 | - | 影院ID |  |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ halls | array | 否 |  | 影厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|   └ goodsTypeIds | array | 否 | - | 优惠券适用商品类型 | 0,0 |
|   └ goods | array | 否 |  | 优惠券适用指定商品 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ cinemaId | int64 | 否 | - | 影院ID | 0 |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ goodsId | string | 否 | - | 卖品ID |  |
|     └ goodsName | string | 否 | - | 卖品名称 |  |
| exhibitionRestriction | object | 否 |  | 展览优惠券 限制规则 |  |
|   └ exhibitScope | int32 | 否 | - | 展览范围：全部 0，指定 1，排除 2 | 0 |
|   └ exhibitHouses | array | 否 |  | 优惠券适用（场馆&展厅） |  |
|     └ halls | array | 否 |  | 展厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|     └ houseId | string | 否 | - | No comments found. |  |
|   └ exhibits | array | 否 |  | 优惠券适用（展品） |  |
|     └ exhibitId | int64 | 否 | - | 展览ID | 0 |
|     └ exhibitName | string | 否 | - | 展览名称 |  |

#### 响应示例

```
{
    "couponId": "",
    "ticketRestriction": {
        "couponId": "",
        "id": "",
        "cinemaRestriction": {
            "cinemaScope": 0,
            "cinemas": [
                {
                    "id": 0,
                    "cinemaId": "",
                    "cinemaName": "",
                    "halls": [
                        {
                            "hallId": "",
                            "hallName": ""
                        }
                    ]
                }
            ]
        },
        "filmRestriction": {
            "filmScope": 0,
            "films": [
                {
                    "id": 0,
                    "filmId": 0,
                    "filmCode": "",
                    "filmName": ""
                }
            ]
        },
        "periodRestriction": {
            "periodScope": 0,
            "periods": [
                {
                    "id": 0,
                    "num": 0,
                    "day": 0,
                    "start": 0,
                    "end": 0
                }
            ]
        },
        "filmVersionRestriction": {
            "versionScope": 0,
            "versions": [
                {
                    "filmVersion": "",
                    "versionId": ""
                }
            ]
        }
    },
    "showRestriction": {
        "showScope": 0,
        "shows": [
            {
                "cinemaId": 0,
                "cinemaName": "",
                "showName": "",
                "showId": "",
                "id": "",
                "sessions": [
                    {
                        "id": "",
                        "showScheduleId": 0,
                        "startTime": 0,
                        "endTime": 0
                    }
                ]
            }
        ]
    },
    "goodsRestriction": {
        "goodsScope": 0,
        "cinemas": [
            {
                "id": 0,
                "cinemaId": "",
                "cinemaName": "",
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ]
            }
        ],
        "goodsTypeIds": [
            0,
            0
        ],
        "goods": [
            {
                "id": 0,
                "cinemaId": 0,
                "cinemaName": "",
                "goodsId": "",
                "goodsName": ""
            }
        ]
    },
    "exhibitionRestriction": {
        "exhibitScope": 0,
        "exhibitHouses": [
            {
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ],
                "houseId": ""
            }
        ],
        "exhibits": [
            {
                "exhibitId": 0,
                "exhibitName": ""
            }
        ]
    }
}
```

#### 错误码

无
