/**
 * 优惠券模块类型定义
 * 提供完整的类型安全支持
 */

// ==================== 基础类型 ====================

/** 基础响应类型 */
export interface BaseResponse<T = any> {
  code: number
  data: T
  msg: string
  success: boolean
}

/** 分页响应类型 */
export interface PageResponse<T = any> {
  content: T[]
  total: number
  page: number
  size: number
  totalPages: number
}

/** 分页请求参数 */
export interface PageParams {
  page: number
  size: number
  isAsc?: 'asc' | 'desc'
  orderByColumn?: string
}

// ==================== 优惠券相关类型 ====================

/** 优惠券适用类型 */
export enum UseOnType {
  MOVIE = 0, // 电影票
  GOODS = 1, // 卖品
  SHOW = 2, // 演出
  EXHIBITION = 3, // 展览
}

/** 优惠券类型 */
export enum CouponType {
  DISCOUNT = 1, // 满减券
  PERCENTAGE = 2, // 折扣券
  FIXED_AMOUNT = 3, // 固定金额券
  FREE_SHIPPING = 4, // 免邮券
}

/** 生成方式 */
export enum GenerateType {
  ON_DEMAND = 0, // 需要时生成
  BATCH = 1, // 一次性生成
}

/** 使用状态 */
export enum UseStatus {
  DISABLED = 0, // 关闭
  ENABLED = 1, // 开启
}

/** 渠道范围 */
export enum ChannelScope {
  SPECIFIC = 1, // 指定渠道
  ALL = 2, // 全部渠道
}

/** 影院范围 */
export enum CinemaScope {
  ALL = 0, // 全部影院
  INCLUDE = 1, // 指定影院
  EXCLUDE = 2, // 排除影院
}

/** 有效期类型 */
export enum ValidScope {
  FIXED_DATE = 0, // 固定日期
  RELATIVE = 1, // 相对日期
}

// ==================== 优惠券数据结构 ====================

/** 渠道信息 */
export interface ChannelInfo {
  id: number
  channelName: string
  channelCode?: string
}

/** 渠道规则 */
export interface ChannelRule {
  channelScope: ChannelScope
  channels?: ChannelInfo[]
}

/** 影院信息 */
export interface CinemaInfo {
  id: number
  cinemaName: string
  cinemaCode?: string
}

/** 影院规则 */
export interface CinemaRule {
  ruleId: string
  cinemaNames: string[]
  cinemas?: CinemaInfo[]
}

/** 优惠券票务选择范围配置 */
export interface CouponTicketSelectScopeConfig {
  ruleId: string
  cinemaScope: CinemaScope
}

/** 优惠券票务配置 */
export interface CouponTicketConfig {
  id?: string
  couponTicketSelectScopeConfig: CouponTicketSelectScopeConfig
}

/** 生成规则 */
export interface GenerateRule {
  generateType: GenerateType
  num?: number
}

/** 有效期规则 */
export interface PeriodRule {
  validScope: ValidScope
  startTime?: number
  endTime?: number
  overdueDay?: number
}

/** 优惠券主要信息 */
export interface CouponItem {
  id: string
  name: string
  useOn: UseOnType
  couponType: CouponType
  useStatus: UseStatus
  bindCount: number
  usedCount: number
  createTime?: number | string
  updateTime?: number | string

  // 规则相关
  channelRule: ChannelRule
  generateRule: GenerateRule
  periodRule: PeriodRule

  // 票务相关
  couponTicketConfigs?: CouponTicketConfig[]
  couponRuleCinemas?: CinemaRule[]

  // 其他配置
  couponCodeCreateConfig?: {
    useStatus: UseStatus
  }
}

// ==================== 请求参数类型 ====================

/** 优惠券列表查询参数 */
export interface CouponListParams extends PageParams {
  id?: number | string
  name?: string
  useOn?: UseOnType | string
  couponType?: CouponType | string
  validStartTime?: string
  validEndTime?: string
  useStatus?: UseStatus | string
  generateType?: GenerateType | string
  channelId?: number | string
  applyFilmType?: string
  applyCinema?: string
}

/** 优惠券状态更新参数 */
export interface CouponStatusParams {
  id: string
  useStatus: UseStatus
}

/** 优惠券创建参数 */
export interface CreateCouponParams {
  name: string
  useOn: UseOnType
  couponType: CouponType
  discountAmount?: number
  minAmount?: number
  generateRule: GenerateRule
  periodRule: PeriodRule
  channelRule: ChannelRule
  couponTicketConfigs?: CouponTicketConfig[]
}

/** 优惠券更新参数 */
export interface UpdateCouponParams extends Partial<CreateCouponParams> {
  id: string
}

// ==================== 表单相关类型 ====================

/** 表单验证规则 */
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any, callback: any) => void
  min?: number
  max?: number
  pattern?: RegExp
}

/** 表单字段配置 */
export interface FormFieldConfig {
  [key: string]: FormRule[]
}

/** 编辑模式 */
export enum EditMode {
  CREATE = 'create',
  EDIT = 'edit',
  VIEW = 'view',
}

/** 编辑步骤 */
export enum EditStep {
  BASIC = 1, // 基本信息
  CONFIG = 2, // 配置信息
  CONFIRM = 3, // 确认信息
}

/** 编辑优惠券数据 */
export interface EditCouponData {
  mode: EditMode
  step?: EditStep
  data?: CouponItem
}

export interface ChannelOption {
  id: number
  label: string
  value?: any
}

// ==================== 组件Props类型 ====================

/** 表格组件Props */
export interface CouponTableProps {
  data: CouponItem[]
  loading?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
  tableHeight?: number | string
}

/** 搜索表单Props */
export interface CouponSearchFormProps {
  modelValue: CouponListParams
  channelOptions?: ChannelOption[]
  loading?: boolean
}

/** 渠道选项 */
export interface ChannelOption {
  id: number
  label: string
  value?: any
}

// ==================== 常量选项类型 ====================

/** 选项配置 */
export interface OptionConfig {
  value: number | string
  label: string
  des?: {
    type?: string
    color?: string
  }
}

/** 适用类型选项 */
export type UseOnOptions = OptionConfig[]

/** 优惠券类型选项 */
export type CouponTypeOptions = OptionConfig[]

// ==================== 工具类型 ====================

/** 深度可选 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/** 深度必需 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

/** 选择性必需 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

/** 选择性可选 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// ==================== API响应类型 ====================

/** 优惠券列表响应 */
export type CouponListResponse = BaseResponse<PageResponse<CouponItem>>

/** 优惠券详情响应 */
export type CouponDetailResponse = BaseResponse<CouponItem>

/** 优惠券创建响应 */
export type CreateCouponResponse = BaseResponse<{ id: string }>

/** 优惠券更新响应 */
export type UpdateCouponResponse = BaseResponse<boolean>

/** 优惠券删除响应 */
export type DeleteCouponResponse = BaseResponse<boolean>

// ==================== 类型守卫 ====================

/** 检查是否为有效的优惠券项 */
export function isCouponItem(item: any): item is CouponItem {
  return (
    item
    && typeof item.id === 'string'
    && typeof item.name === 'string'
    && typeof item.useOn === 'number'
    && typeof item.couponType === 'number'
    && typeof item.useStatus === 'number'
  )
}

/** 检查是否为有效的分页响应 */
export function isPageResponse<T>(response: any): response is PageResponse<T> {
  return (
    response
    && Array.isArray(response.content)
    && typeof response.total === 'number'
    && typeof response.page === 'number'
    && typeof response.size === 'number'
  )
}

/** 检查是否为成功响应 */
export function isSuccessResponse<T>(response: any): response is BaseResponse<T> {
  return (
    response
    && typeof response.code === 'number'
    && response.code === 200
    && response.success === true
  )
}
