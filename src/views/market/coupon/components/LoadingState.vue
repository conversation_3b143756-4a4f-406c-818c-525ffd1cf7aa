<!--
 * 加载状态组件
 * 提供统一的加载、空状态、错误状态显示
-->

<template>
  <div class="loading-state">
    <!-- 加载中状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="skeletonRows" animated />
      <div class="loading-text">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>{{ loadingText }}</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-empty
        :image-size="120"
        description="加载失败"
      >
        <template #image>
          <el-icon size="120" color="#f56c6c">
            <Warning />
          </el-icon>
        </template>
        <template #description>
          <p class="error-message">{{ errorMessage }}</p>
        </template>
        <el-button type="primary" @click="handleRetry">
          <template #icon>
            <el-icon>
              <Refresh />
            </el-icon>
          </template>
          重试
        </el-button>
      </el-empty>
    </div>

    <!-- 空状态 -->
    <div v-else-if="isEmpty" class="empty-container">
      <el-empty
        :image-size="160"
        :description="emptyDescription"
      >
        <template #image>
          <el-icon size="160" color="#909399">
            <Box />
          </el-icon>
        </template>
        <el-button v-if="showCreateButton" type="primary" @click="handleCreate">
          <template #icon>
            <el-icon>
              <Plus />
            </el-icon>
          </template>
          {{ createButtonText }}
        </el-button>
      </el-empty>
    </div>

    <!-- 正常内容 -->
    <div v-else class="content-container">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Loading, Warning, Refresh, Box, Plus } from '@element-plus/icons-vue'

interface Props {
  loading?: boolean
  error?: boolean
  isEmpty?: boolean
  loadingText?: string
  errorMessage?: string
  emptyDescription?: string
  skeletonRows?: number
  showCreateButton?: boolean
  createButtonText?: string
}

interface Emits {
  (e: 'retry'): void
  (e: 'create'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: false,
  isEmpty: false,
  loadingText: '加载中...',
  errorMessage: '数据加载失败，请稍后重试',
  emptyDescription: '暂无数据',
  skeletonRows: 5,
  showCreateButton: false,
  createButtonText: '新建',
})

const emit = defineEmits<Emits>()

const handleRetry = () => {
  emit('retry')
}

const handleCreate = () => {
  emit('create')
}
</script>

<style scoped lang="scss">
.loading-state {
  min-height: 200px;
  
  .loading-container {
    padding: 20px;
    
    .loading-text {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      color: #909399;
      font-size: 14px;
      
      .el-icon {
        margin-right: 8px;
      }
    }
  }
  
  .error-container,
  .empty-container {
    padding: 40px 20px;
    
    .error-message {
      color: #f56c6c;
      margin: 10px 0;
    }
  }
  
  .content-container {
    min-height: inherit;
  }
}
</style>
