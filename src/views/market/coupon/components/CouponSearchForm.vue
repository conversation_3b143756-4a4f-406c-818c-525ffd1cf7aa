<!--
 * 优惠券搜索表单组件
 * 提供搜索条件输入和重置功能
-->

<script setup lang="ts">
import type { CouponListParams } from '@/api/modules/market/coupon'
import { reactive, ref, watch } from 'vue'
import { COUPON_TYPE_OPTIONS, USE_ON_OPTIONS } from '../module/constants'

interface ChannelOption {
  id: number
  label: string
}

interface Props {
  modelValue: CouponListParams
  channelOptions?: ChannelOption[]
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: CouponListParams): void
  (e: 'search'): void
  (e: 'reset'): void
  (e: 'coupon-code-query'): void
  (e: 'add'): void
}

const props = withDefaults(defineProps<Props>(), {
  channelOptions: () => [
    { id: 0, label: '全部' },
    { id: 1, label: '抖音' },
    { id: 2, label: '快手' },
    { id: 3, label: '微信' },
    { id: 4, label: '小程序' },
  ],
  loading: false,
})

const emit = defineEmits<Emits>()

const formRef = ref()
const dateRange = ref<string[]>([])

// 搜索参数
const searchParams = reactive<CouponListParams>({ ...props.modelValue })

// 监听外部参数变化
watch(
  () => props.modelValue,
  (newValue) => {
    Object.assign(searchParams, newValue)
  },
  { deep: true },
)

// 监听内部参数变化，同步到外部
watch(
  searchParams,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

// 处理日期范围变化
function handleDateRangeChange(dates: string[]) {
  if (dates && dates.length === 2) {
    searchParams.validStartTime = dates[0]
    searchParams.validEndTime = dates[1]
  }
  else {
    searchParams.validStartTime = undefined
    searchParams.validEndTime = undefined
  }
}

// 搜索
function handleSearch() {
  emit('search')
}

// 重置
function handleReset() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  dateRange.value = []

  // 重置搜索参数
  Object.assign(searchParams, {
    page: 1,
    size: 10,
    id: '',
    name: undefined,
    useOn: '',
    couponType: '',
    validStartTime: undefined,
    validEndTime: undefined,
    useStatus: '',
    generateType: '',
    channelId: undefined,
  })

  emit('reset')
}

// 查询券码
function handleCouponCodeQuery() {
  emit('coupon-code-query')
}

// 新增
function handleAdd() {
  emit('add')
}

// 暴露方法给父组件
defineExpose({
  resetFields: () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
  },
})
</script>

<template>
  <div class="coupon-search-form">
    <el-form
      ref="formRef"
      :model="searchParams"
      size="small"
      :inline="true"
      @submit.prevent="handleSearch"
    >
      <el-form-item label="优惠券ID" prop="id">
        <el-input
          v-model="searchParams.id"
          placeholder="请输入优惠券ID"
          clearable
          style="width: 200px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>

      <el-form-item label="优惠券名称" prop="name">
        <el-input
          v-model="searchParams.name"
          placeholder="请输入优惠券名称"
          clearable
          style="width: 200px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>

      <el-form-item label="适用类型" prop="useOn">
        <el-select
          v-model="searchParams.useOn"
          placeholder="请选择适用类型"
          clearable
          style="width: 200px;"
        >
          <el-option
            v-for="item in USE_ON_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="渠道" prop="channelId">
        <el-select
          v-model="searchParams.channelId"
          placeholder="请选择渠道"
          clearable
          style="width: 200px;"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="优惠券类型" prop="couponType">
        <el-select
          v-model="searchParams.couponType"
          placeholder="请选择优惠券类型"
          clearable
          style="width: 200px;"
        >
          <el-option
            v-for="item in COUPON_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="生成方式" prop="generateType">
        <el-select
          v-model="searchParams.generateType"
          placeholder="请选择生成方式"
          clearable
          style="width: 200px;"
        >
          <el-option label="全部" value="" />
          <el-option label="一次性生成" value="1" />
          <el-option label="需要时生成" value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="使用状态" prop="useStatus">
        <el-select
          v-model="searchParams.useStatus"
          placeholder="请选择使用状态"
          clearable
          style="width: 200px;"
        >
          <el-option label="全部" value="" />
          <el-option label="开启" :value="1" />
          <el-option label="关闭" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="有效日期">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 1, 1, 23, 59, 59),
          ]"
          style="width: 260px;"
          @change="handleDateRangeChange"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          查询
        </el-button>

        <el-button @click="handleReset">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          重置
        </el-button>

        <el-button type="success" @click="handleCouponCodeQuery">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          查询券码
        </el-button>

        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增券
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.coupon-search-form {
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 4px;

  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
