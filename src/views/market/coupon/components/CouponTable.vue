<!--
 * 优惠券表格组件
 * 支持虚拟滚动、排序、筛选等功能
-->

<script setup lang="ts">
import type { CouponItem } from '@/api/modules/market/coupon'
import dayjs from 'dayjs'
import { computed, ref, watch } from 'vue'
import { COUPON_TYPE_OPTIONS, getCouponTypeOption, USE_ON_OPTIONS } from '../module/constants'

interface Props {
  data: CouponItem[]
  loading?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
  tableHeight?: number | string
}

interface Emits {
  (e: 'selection-change', selection: CouponItem[]): void
  (e: 'row-dblclick', row: CouponItem): void
  (e: 'sort-change', { prop, order }: { prop: string, order: string }): void
  (e: 'size-change', size: number): void
  (e: 'current-change', page: number): void
  (e: 'status-change', row: CouponItem): void
  (e: 'detail', row: CouponItem): void
  (e: 'show-coupon-code', row: CouponItem): void
  (e: 'copy', row: CouponItem): void
  (e: 'edit-base', row: CouponItem): void
  (e: 'edit-config', row: CouponItem): void
  (e: 'delete', row: CouponItem): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  tableHeight: 600,
})

const emit = defineEmits<Emits>()

const tableRef = ref()

// 计算显示的数据（用于虚拟滚动优化）
const displayData = computed(() => {
  // 如果数据量大，可以在这里实现虚拟滚动逻辑
  return props.data
})

// 工具函数
function getUseOnLabel(value: number) {
  return USE_ON_OPTIONS.find(item => item.value === value)?.label || '未知'
}

function getUseOnTagType(value: number) {
  const typeMap = {
    0: 'success', // 电影票
    1: 'primary', // 卖品
    2: 'warning', // 演出
    3: 'info', // 展览
  }
  return typeMap[value] || 'info'
}

function formatTimestamp(timestamp?: number) {
  if (!timestamp) { return '无效' }
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm')
}

// 事件处理
function handleSelectionChange(selection: CouponItem[]) {
  emit('selection-change', selection)
}

function handleRowDoubleClick(row: CouponItem) {
  emit('row-dblclick', row)
}

function handleSortChange({ prop, order }: { prop: string, order: string }) {
  emit('sort-change', { prop, order })
}

function handleSizeChange(size: number) {
  emit('size-change', size)
}

function handleCurrentChange(page: number) {
  emit('current-change', page)
}

function handleStatusChange(row: CouponItem) {
  emit('status-change', row)
}

function handleDetail(row: CouponItem) {
  emit('detail', row)
}

function handleShowCouponCode(row: CouponItem) {
  emit('show-coupon-code', row)
}

function handleCopy(row: CouponItem) {
  emit('copy', row)
}

function handleEditBase(row: CouponItem) {
  emit('edit-base', row)
}

function handleEditConfig(row: CouponItem) {
  emit('edit-config', row)
}

function handleDelete(row: CouponItem) {
  emit('delete', row)
}
</script>

<template>
  <div class="coupon-table">
    <el-table
      ref="tableRef" v-loading="loading" :data="displayData" @selection-change="handleSelectionChange"
      @row-dblclick="handleRowDoubleClick" @sort-change="handleSortChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->

      <el-table-column label="优惠券ID" prop="id" sortable="custom" show-overflow-tooltip>
        <template #default="{ row }">
          <el-text>
            {{ row.id }}
          </el-text>
        </template>
      </el-table-column>

      <el-table-column label="优惠券名称" prop="name" width="160" sortable="custom" show-overflow-tooltip />

      <el-table-column label="适用商品" prop="useOn">
        <template #default="{ row }">
          <el-tag :type="getUseOnTagType(row.useOn)">
            {{ getUseOnLabel(row.useOn) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="券类型" prop="couponType">
        <template #default="{ row }">
          <el-tag :type="getCouponTypeOption(row.couponType)?.des?.type">
            {{ getCouponTypeOption(row.couponType)?.label }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="适用渠道">
        <template #default="{ row }">
          <el-tag v-if="row.channelRule?.channelScope === 2">
            全部渠道
          </el-tag>
          <el-popover v-else placement="bottom" trigger="hover" width="230">
            <el-card shadow="never">
              <template #header>
                <span>适用渠道：</span>
              </template>
              <el-tag v-for="(item, index) in row.channelRule?.channels" :key="index" style="margin: 2px;">
                {{ item.channelName }}
              </el-tag>
            </el-card>
            <template #reference>
              <el-button link type="primary">
                包含渠道 {{ row.channelRule?.channels?.length || 0 }} 个
              </el-button>
            </template>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="生成方式">
        <template #default="{ row }">
          <span v-if="row.generateRule?.generateType">
            生成
            <el-tag type="success">{{ row.generateRule.num }}</el-tag>
            张
          </span>
          <el-tag v-else type="warning">
            需要生成
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="券绑定用户数" prop="bindCount" sortable="custom" />

      <el-table-column label="券已使用数" prop="usedCount" sortable="custom" />

      <el-table-column label="有效日期">
        <template #default="{ row }">
          <span v-if="row.periodRule?.validScope">
            绑后
            <el-tag type="success">{{ row.periodRule.overdueDay }}</el-tag>
            天有效
          </span>
          <span v-else>
            {{ formatTimestamp(row.periodRule?.startTime) }} 至
            {{ formatTimestamp(row.periodRule?.endTime) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="使用状态">
        <template #default="{ row }">
          <el-switch
            v-model="row.useStatus" inline-prompt active-text="开启" inactive-text="关闭" :active-value="1"
            :inactive-value="0" @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <div class="flex gap-2">
            <!-- <el-button type="primary" link @click="handleDetail(row)">
              详情
            </el-button>
            <el-button type="primary" link @click="handleShowCouponCode(row)">
              查看券码
            </el-button> -->
            <el-dropdown trigger="click">
              <el-button type="primary" link>
                更多
                <FaIcon name="ep:arrow-down" />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleDetail(row)">
                    <FaIcon name="ep:chat-line-square" />
                    详情
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleShowCouponCode(row)">
                    <FaIcon name="eye" />
                    查看券码
                  </el-dropdown-item>
                  <el-dropdown-item disabled @click="handleCopy(row)">
                    <FaIcon name="copy" />
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEditBase(row)">
                    <FaIcon name="edit" />
                    修改主信息
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEditConfig(row)">
                    <FaIcon name="edit" />
                    修改条件
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDelete(row)">
                    <FaIcon name="delete" />
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      :current-page="currentPage" :page-size="pageSize" :total="total" :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper" class="mt-4 text-right" @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
// .coupon-table {
//   .flex {
//     display: flex;
//     align-items: center;
//   }

//   .gap-2 {
//     gap: 8px;
//   }
// }
</style>
