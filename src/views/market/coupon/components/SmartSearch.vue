<!--
 * 智能搜索组件
 * 支持防抖、历史记录、自动完成等功能
-->

<template>
  <div class="smart-search">
    <el-input
      ref="inputRef"
      v-model="searchValue"
      :placeholder="placeholder"
      :size="size"
      :disabled="disabled"
      :clearable="clearable"
      class="search-input"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keyup.enter="handleEnter"
      @clear="handleClear"
    >
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
      
      <template #suffix>
        <el-button
          v-if="showSearchButton"
          type="primary"
          size="small"
          :loading="searching"
          @click="handleSearch"
        >
          搜索
        </el-button>
      </template>
    </el-input>

    <!-- 搜索建议下拉框 -->
    <div
      v-if="showSuggestions && (suggestions.length > 0 || searchHistory.length > 0)"
      class="suggestions-dropdown"
    >
      <!-- 搜索建议 -->
      <div v-if="suggestions.length > 0" class="suggestions-section">
        <div class="section-title">搜索建议</div>
        <div
          v-for="(suggestion, index) in suggestions"
          :key="`suggestion-${index}`"
          class="suggestion-item"
          @click="handleSelectSuggestion(suggestion)"
        >
          <el-icon class="suggestion-icon">
            <Search />
          </el-icon>
          <span class="suggestion-text" v-html="highlightMatch(suggestion)"></span>
        </div>
      </div>

      <!-- 搜索历史 -->
      <div v-if="searchHistory.length > 0 && !searchValue" class="history-section">
        <div class="section-title">
          <span>搜索历史</span>
          <el-button
            type="text"
            size="small"
            @click="clearHistory"
          >
            清空
          </el-button>
        </div>
        <div
          v-for="(history, index) in searchHistory"
          :key="`history-${index}`"
          class="history-item"
          @click="handleSelectHistory(history)"
        >
          <el-icon class="history-icon">
            <Clock />
          </el-icon>
          <span class="history-text">{{ history }}</span>
          <el-icon
            class="remove-icon"
            @click.stop="removeHistory(index)"
          >
            <Close />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { Search, Clock, Close } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

interface Props {
  modelValue?: string
  placeholder?: string
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  clearable?: boolean
  showSearchButton?: boolean
  debounceDelay?: number
  maxHistoryCount?: number
  suggestions?: string[]
  searching?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', value: string): void
  (e: 'input', value: string): void
  (e: 'clear'): void
  (e: 'focus'): void
  (e: 'blur'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入搜索关键词',
  size: 'default',
  disabled: false,
  clearable: true,
  showSearchButton: false,
  debounceDelay: 300,
  maxHistoryCount: 10,
  suggestions: () => [],
  searching: false,
})

const emit = defineEmits<Emits>()

const inputRef = ref()
const searchValue = ref(props.modelValue)
const showSuggestions = ref(false)
const searchHistory = ref<string[]>([])

// 历史记录存储key
const HISTORY_STORAGE_KEY = 'coupon-search-history'

// 同步外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    searchValue.value = newValue
  }
)

// 防抖搜索
const debouncedSearch = debounce((value: string) => {
  emit('input', value)
}, props.debounceDelay)

// 处理输入
const handleInput = (value: string) => {
  searchValue.value = value
  emit('update:modelValue', value)
  debouncedSearch(value)
}

// 处理搜索
const handleSearch = () => {
  const trimmedValue = searchValue.value.trim()
  if (trimmedValue) {
    addToHistory(trimmedValue)
    emit('search', trimmedValue)
    showSuggestions.value = false
  }
}

// 处理回车
const handleEnter = () => {
  handleSearch()
}

// 处理清空
const handleClear = () => {
  searchValue.value = ''
  emit('update:modelValue', '')
  emit('clear')
  showSuggestions.value = false
}

// 处理焦点
const handleFocus = () => {
  showSuggestions.value = true
  emit('focus')
}

// 处理失焦
const handleBlur = () => {
  // 延迟隐藏，允许点击建议项
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
  emit('blur')
}

// 选择搜索建议
const handleSelectSuggestion = (suggestion: string) => {
  searchValue.value = suggestion
  emit('update:modelValue', suggestion)
  addToHistory(suggestion)
  emit('search', suggestion)
  showSuggestions.value = false
}

// 选择搜索历史
const handleSelectHistory = (history: string) => {
  searchValue.value = history
  emit('update:modelValue', history)
  emit('search', history)
  showSuggestions.value = false
}

// 高亮匹配文本
const highlightMatch = (text: string) => {
  if (!searchValue.value) return text
  
  const regex = new RegExp(`(${searchValue.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 添加到搜索历史
const addToHistory = (value: string) => {
  if (!value.trim()) return
  
  // 移除重复项
  const index = searchHistory.value.indexOf(value)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  
  // 添加到开头
  searchHistory.value.unshift(value)
  
  // 限制历史记录数量
  if (searchHistory.value.length > props.maxHistoryCount) {
    searchHistory.value = searchHistory.value.slice(0, props.maxHistoryCount)
  }
  
  // 保存到本地存储
  saveHistory()
}

// 移除历史记录
const removeHistory = (index: number) => {
  searchHistory.value.splice(index, 1)
  saveHistory()
}

// 清空历史记录
const clearHistory = () => {
  searchHistory.value = []
  saveHistory()
}

// 保存历史记录到本地存储
const saveHistory = () => {
  try {
    localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(searchHistory.value))
  } catch (error) {
    console.error('保存搜索历史失败:', error)
  }
}

// 从本地存储加载历史记录
const loadHistory = () => {
  try {
    const saved = localStorage.getItem(HISTORY_STORAGE_KEY)
    if (saved) {
      searchHistory.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error)
    searchHistory.value = []
  }
}

// 点击外部关闭建议框
const handleClickOutside = (event: Event) => {
  if (inputRef.value && !inputRef.value.$el.contains(event.target)) {
    showSuggestions.value = false
  }
}

// 组件挂载时加载历史记录
onMounted(() => {
  loadHistory()
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 暴露方法给父组件
defineExpose({
  focus: () => {
    inputRef.value?.focus()
  },
  blur: () => {
    inputRef.value?.blur()
  },
  clear: handleClear,
})
</script>

<style scoped lang="scss">
.smart-search {
  position: relative;
  
  .search-input {
    width: 100%;
  }
  
  .suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      font-size: 12px;
      color: #909399;
      background: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
    }
    
    .suggestion-item,
    .history-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &:hover {
        background: #f5f7fa;
      }
      
      .suggestion-icon,
      .history-icon {
        margin-right: 8px;
        color: #909399;
        font-size: 14px;
      }
      
      .suggestion-text,
      .history-text {
        flex: 1;
        font-size: 14px;
        
        :deep(mark) {
          background: #409eff;
          color: white;
          padding: 0 2px;
          border-radius: 2px;
        }
      }
      
      .remove-icon {
        margin-left: 8px;
        color: #c0c4cc;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.2s;
        
        &:hover {
          color: #f56c6c;
        }
      }
      
      &:hover .remove-icon {
        opacity: 1;
      }
    }
  }
}
</style>
