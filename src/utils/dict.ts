// import { getDicts } from '@/api/system/dict/data'
import useDictStore from '@/store/modules/dict'

interface dictItem {
  createBy?: string,
  createTime?: string,
  updateBy?: string,
  updateTime?: string,
  dictCode?: number,
  dictSort?: number,
  dictLabel?: string,
  dictValue?: string,
  dictType?: string,
  cssClass?: string,
  listClass?: string,
  isDefault?: string,
  status?: string,
  remark?: string,
  default?: boolean
}

const dictList = ref(
  [
    {
      dictType: 'sys_user_sex',
      dictLabel: '男',
      dictValue: '0',
    },
    {
      dictType: 'sys_user_sex',
      dictLabel: '女',
      dictValue: '1',
    }
  ]
)

function getDictInfo(dictType: string) {
  return new Promise((resolve, reject) => {
    const dicts = dictList.value.filter((p: dictItem) => p.dictType === dictType)
      resolve({
        code: 200,
        msg: '操作成功',
        data: dicts
      })
  })
}

/**
 * 获取字典数据
 */
export function useDict(...args: string[]) {
  const res = ref({} as Record<string, any>)
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts
      }
      else {
        getDictInfo(dictType).then(resp => {
          res.value[dictType] = resp.data.map(p => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass, elTagClass: p.cssClass }))
          useDictStore().setDict(dictType, res.value[dictType]);
        })
      }
    })
    return toRefs(res.value)
  })()
}
