import type {
  ApiResponse,
  Cinema,
  CinemaSearchParams,
  CinemaSearchResponse,
  FilmSearchParams,
  FilmSearchResponse,
  PageResponse,
  ScheduleSearchParams,
  ScheduleSearchResponse,
  Screen,
  ScreenSearchParams,
} from '@/types/cinema'
import api from '@/api'

// 影院管理相关接口

export default {

  // 获取影院列表
  getCinemaList: (params: CinemaSearchParams): Promise<ApiResponse<CinemaSearchResponse>> =>
    api.post('/cinema/search', params),

  // 获取所有影院列表（不分页）
  getAllCinemaList: (): Promise<ApiResponse<Cinema[]>> =>
    api.post('/data/cinema/listAll'),

  // 获取影院详情
  getCinemaDetail: (params: { cinemaCode: string }): Promise<ApiResponse<Cinema>> =>
    api.post('/data/cinema/getByCode', params),

  // 生成影院二维码
  createCinemaQrcode: (params: { cinemaCode: string }): Promise<ApiResponse<{ codeUrl: string }>> =>
    api.post('/cinema/qrcode/create', params),

  // ==================== 影片管理接口 ====================

  // 获取影片列表
  getFilmList: (params: FilmSearchParams): Promise<ApiResponse<FilmSearchResponse>> =>
    api.post('/data/film/search', params, {
      // 接口权限：/data/film/query
    }),

  // 获取影片详情
  getFilmDetail: (params: {
    filmId: string
  }): Promise<ApiResponse<any>> =>
    api.post('/data/film/detail', params, {
      // 接口权限：/data/film/query
    }),

  // ==================== 场次管理接口 ====================

  // 获取场次列表
  getScheduleList: (params: ScheduleSearchParams): Promise<ApiResponse<ScheduleSearchResponse>> =>
    api.post('/ticket/play/search', params, {
      // 接口权限：/ticket/play/query
    }),

  // 获取场次详情
  getScheduleDetail: (params: {
    scheduleId: string
  }): Promise<ApiResponse<any>> =>
    api.post('/ticket/play/detail', params, {
      // 接口权限：/ticket/play/query
    }),

  // 获取场次座位信息
  getScheduleSeats: (params: {
    scheduleCode: string
  }) => api.post('/ticket/play/seats', params, {
    // 接口权限：/ticket/play/query
  }),

  // ==================== 影厅管理接口 ====================

  // 获取影厅列表
  getScreenList: (params: ScreenSearchParams): Promise<ApiResponse<PageResponse<Screen>>> =>
    api.post('/data/screen/search', params, {
      // 接口权限：/data/screen/query
    }),

  // 获取影厅详情
  getScreenDetail: (params: {
    screenCode: string
  }): Promise<ApiResponse<Screen>> => api.post('/data/screen/detail', params, {
    // 接口权限：/data/screen/query
  }),

  // // ==================== 票务相关接口 ====================

  // // 获取票务统计
  // getTicketStats: (params: {
  //   cinemaCode?: string
  //   startDate?: string
  //   endDate?: string
  // }) => api.post('/ticket/stats', params, {
  //   // 接口权限：/ticket/stats/query
  // }),

  // // 获取座位锁定状态
  // getSeatLockStatus: (params: {
  //   scheduleCode: string
  //   seatCodes: string[]
  // }) => api.post('/ticket/seat/lock/status', params, {
  //   // 接口权限：/ticket/play/query
  // }),

  // // 锁定座位
  // lockSeats: (params: {
  //   scheduleCode: string
  //   seatCodes: string[]
  //   lockTime?: number // 锁定时长，单位分钟
  // }) => api.post('/ticket/seat/lock', params, {
  //   // 接口权限：/ticket/seat/lock
  // }),

  // // 解锁座位
  // unlockSeats: (params: {
  //   scheduleCode: string
  //   seatCodes: string[]
  // }) => api.post('/ticket/seat/unlock', params, {
  //   // 接口权限：/ticket/seat/unlock
  // }),

  // // ==================== 营销活动接口 ====================

  // // 获取营销活动列表
  // getMarketingList: (params: MarketingSearchParams): Promise<ApiResponse<PageResponse<any>>> =>
  //   api.post('/marketing/search', params, {
  //     // 接口权限：/marketing/query
  //   }),

  // // 获取营销活动详情
  // getMarketingDetail: (params: {
  //   marketingCode: string
  // }) => api.post('/marketing/detail', params, {
  //   // 接口权限：/marketing/query
  // }),
}
