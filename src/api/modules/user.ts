import md5 from 'js-md5'
import api from '../index'

// 用户接口定义
export interface User {
  id: string
  areaCode: number
  channel: number
  account: string
  name: string
  headImage: string
  companys: Company[]
  referralId: string
  gender: number
  ctime: number
  note: string
  deleted: boolean
  lastActiveDay: number
  operator: string
  banlance: number
  coupons: number
  benefitCards: number
  vipCardBalance: number
}

export interface Company {
  id: string
  name: string
  province: string
  provinceCode: string
  city: string
  cityCode: string
  ctime: number
  available: boolean
}

export interface UserListParams {
  page: number
  size: number
  account?: string
  name?: string
  referralName?: string
  companyId?: string
  timeSection?: {
    begin: number
    end: number
  }
}

export interface UserListResponse {
  total: number
  content: User[]
  result: User[]
}

export interface ResetPasswordData {
  id: string
  password: string
}

export interface BindCompanyData {
  ids: string[]
  companyId: string
}

export default {
  // 登录（对接新接口）
  login: (data: {
    account: string
    password: string
  }) => api.post(
    '/adm_account/signInWithPassword',
    {
      account: data.account,
      password: md5(data.password), // 需md5加密
    },
    {
      headers: { 'Content-Type': 'application/json' },
    },
  ),

  // 获取权限（对接新接口）
  permission: () => api.post(
    '/adm_account/getMyInfo',
    {},
    {
    },
  ),

  // 修改密码
  passwordEdit: (data: {
    password: string
    newPassword: string
  }) => api.post('/adm_account/resetPassword', data, {
    // baseURL: '/mock/',
  }),

  // 用户查询
  getUserList: (params: UserListParams) => api.post<UserListResponse>('/adm_user/search', params),

  // 用户详情
  getUserDetail: (id: string) => api.post<User>('/adm_user/getDetailById', { id }),

  // 重置密码
  resetPassword: (data: ResetPasswordData) => api.post('/adm_user/resetPassword', data),

  // 企业绑定
  bindCompany: (data: BindCompanyData) => api.post('/adm_user/bindCompany', data),

  // 企业解绑
  unbindCompany: (data: BindCompanyData) => api.post('/adm_user/unbindCompany', data),
}
