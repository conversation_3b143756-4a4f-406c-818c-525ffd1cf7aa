import api from '@/api'

// 电影列表查询参数
export interface FilmListParams {
  page?: number
  size?: number
  filmName?: string
  version?: string
  provider?: string
}

// 电影人员信息
export interface Marker {
  code: string
  names: string[]
  role: string
}

// 电影详情数据
export interface FilmItem {
  id: string
  cinemaCode: string
  code: string
  name: string
  cnName: string
  enName: string
  category: string
  type: string
  lang: string
  duration: number
  sequence: number
  version: string
  publishDate: number
  releaseStatus: string // 0即将上映 1热映中 2已下映
  markers: Marker[]
  introduction: string
  provider: string
}

// 电影列表响应数据
export interface FilmListResponse {
  total: number
  content: FilmItem[]
  result: FilmItem[]
}

// 获取电影列表
export function getFilmList(params: FilmListParams) {
  return api<FilmListResponse>({
    url: '/data/film/search',
    method: 'post',
    data: params
  })
}

// 获取电影详情
export function getFilmDetail(id: string) {
  return api<FilmItem>({
    url: `/data/film/${id}`,
    method: 'get'
  })
}

export default {
  getFilmList,
  getFilmDetail
}
