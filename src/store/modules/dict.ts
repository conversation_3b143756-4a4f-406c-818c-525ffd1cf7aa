import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useDictStore = defineStore('dict', () => {
  const dict = ref([] as Array<{ key: string, value: any }>)

  // 获取字典
  function getDict(_key: string) {
    if (_key == null || _key === '') {
      return null
    }
    try {
      const item = dict.value.find((d: { key: string }) => d.key === _key)
      return item ? item.value : null
    }
    catch (e) {
      return null
    }
  }

  // 设置字典
  function setDict(_key: string, value: any) {
    if (_key !== null && _key !== '') {
      (dict.value as Array<{ key: string, value: any }>).push({ key: _key, value })
    }
  }

  // 删除字典
  function removeDict(_key: string) {
    try {
      const index = (dict.value as Array<{ key: string, value: any }>).findIndex(d => d.key === _key)
      if (index !== -1) {
        (dict.value as Array<{ key: string, value: any }>).splice(index, 1)
        return true
      }
    }
    catch (e) {
      return false
    }
    return false
  }

  // 清空字典
  function cleanDict() {
    dict.value = [] as Array<{ key: string, value: any }>
  }

  // 初始字典
  function initDict() {
    // 初始化逻辑
  }

  return { dict, getDict, setDict, removeDict, cleanDict, initDict }
})

export default useDictStore
