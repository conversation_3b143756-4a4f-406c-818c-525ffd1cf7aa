{"type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build:example": "vue-tsc -b && vite build --mode example", "build:prod": "vue-tsc -b && vite build --mode prod", "serve:example": "http-server ./dist-example -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "tsx ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks", "taze": "taze minor -wIr"}, "dependencies": {"@antv/g2plot": "^2.4.33", "@bytemd/plugin-gfm": "^1.22.0", "@bytemd/vue-next": "^1.22.0", "@tinymce/tinymce-vue": "^6.2.0", "@vee-validate/zod": "^4.15.1", "@visactor/vchart": "^2.0.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vueuse/components": "^13.5.0", "@vueuse/core": "^13.5.0", "@vueuse/integrations": "^13.5.0", "animate.css": "^4.1.1", "axios": "^1.10.0", "bytemd": "^1.22.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "echarts": "^5.6.0", "element-plus": "^2.10.4", "eruda": "^3.4.3", "es-toolkit": "^1.39.7", "hotkeys-js": "^3.13.15", "js-md5": "^0.8.3", "lucide-vue-next": "^0.525.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.3", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "reka-ui": "^2.3.2", "scule": "^1.3.0", "splitpanes": "^4.0.4", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "tinymce": "^7.9.1", "ua-parser-js": "^2.0.4", "vconsole": "^3.15.1", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-currency-input": "^3.2.1", "vue-data-ui": "^2.15.4", "vue-esign": "^1.1.4", "vue-hooks-plus": "^2.4.0", "vue-router": "^4.5.1", "vue-sonner": "^2.0.2", "vxe-table": "^4.14.4", "xe-utils": "^3.7.8", "zod": "^3.25.76"}, "devDependencies": {"@antfu/eslint-config": "^4.17.0", "@clack/prompts": "^0.11.0", "@faker-js/faker": "^9.9.0", "@iconify/json": "^2.2.359", "@iconify/vue": "^5.0.0", "@stylistic/stylelint-config": "^2.0.0", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.14.0", "@types/splitpanes": "^2.2.6", "@unocss/eslint-plugin": "^66.3.3", "@unocss/preset-legacy-compat": "^66.3.3", "@vitejs/plugin-legacy": "^7.0.1", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "eslint": "^9.31.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "lint-staged": "^16.1.2", "npm-run-all2": "^8.0.4", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.5.6", "postcss-nested": "^7.0.2", "sass-embedded": "^1.89.2", "simple-git-hooks": "^2.13.0", "stylelint": "^16.21.1", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.12.1", "svgo": "^4.0.0", "taze": "^19.1.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "unocss": "^66.3.3", "unocss-preset-animations": "^1.2.1", "unplugin-auto-import": "^19.3.0", "unplugin-turbo-console": "^2.2.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.5", "vite-plugin-app-loading": "^0.4.0", "vite-plugin-archiver": "^0.2.0", "vite-plugin-banner": "^0.8.1", "vite-plugin-compression2": "^2.2.0", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-pages": "^0.33.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.7", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^2.2.12"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}}