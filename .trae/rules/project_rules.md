---
description:使用中文回复
globs:
alwaysApply: true
---
<!--
 * @Date: 2025-04-21 17:11:13
 * @LastEditors: kev<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-22 12:45:07
 * @FilePath: \psdv6-manage-pro\.trae\rules\project_rules.md
-->

### 回复规范

- 所有的回复尽量都要使用中文进行回复，除非有特殊要求

### 技术栈规范

- 前端框架: Vue 3.2.45 + Element Plus 2.8.8
- 构建工具: Vite 4.5.5
- 状态管理: Pinia 2.0.22
- 路由: Vue Router 4.1.4
- API请求: Axios 0.28.1
- 测试框架: Vitest 0.34.6 (建议版本)
- 代码规范: ESLint + Prettier
- CSS预处理器: Sass 1.69.5

### 开发规范

1. **组件规范**

   - 必须使用 `<script setup>`vue3语法糖
   - 组件命名采用PascalCase
   - Props必须定义类型和默认值
   - 禁止使用Vue2选项式API
2. **状态管理**

   - 必须使用Pinia进行状态管理
   - Store命名采用useXxxStore格式
   - 禁止直接修改store外的状态
3. **API调用**

   - 必须通过 `src/api/`目录下的封装模块
   - 必须使用async/await处理异步
   - 错误处理必须统一拦截

### API限制规范

- 禁止直接操作DOM（禁用document.getElementById等）
- 禁止使用已废弃的Vue2 API（如Vue.extend）
- 禁止使用jQuery等遗留库
- 禁止使用同步XMLHttpRequest
- 表单验证必须使用async-validator
- 禁止使用eval()和with语句
- 禁止使用style内联样式（scoped样式除外）

### 语言规范

- 使用JavaScript进行开发
- 不强制要求TypeScript
- 建议使用JSDoc进行重要类型标注

### 测试规范

1. **单元测试**

   - 框架: Vitest 0.34.6
   - 覆盖率要求: 核心组件≥80%
   - 测试文件位置: `tests/unit/`
2. **E2E测试**

   - 框架: Cypress 12.17.4
   - 测试文件位置: `tests/e2e/`
3. **目录结构**
   tests/
   ├── unit/      # 单元测试
   ├── e2e/       # 端到端测试
   └── __mocks__/ # 模拟数据

### 运行项目

1. 使用 pnpm 安装依赖
2. 使用 pnpm dev 启动项目
